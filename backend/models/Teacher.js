const db = require('./database');

class Teacher {
  static create(teacherData) {
    const { user_id, teacher_id, subject, qualification, experience } = teacherData;
    
    const stmt = db.prepare(`
      INSERT INTO teachers (user_id, teacher_id, subject, qualification, experience)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(user_id, teacher_id, subject, qualification, experience);
    return this.findById(result.lastInsertRowid);
  }

  static findById(id) {
    const stmt = db.prepare(`
      SELECT t.*, u.name, u.email, u.phone
      FROM teachers t
      JOIN users u ON t.user_id = u.id
      WHERE t.id = ?
    `);
    return stmt.get(id);
  }

  static findByUserId(user_id) {
    const stmt = db.prepare(`
      SELECT t.*, u.name, u.email, u.phone
      FROM teachers t
      JOIN users u ON t.user_id = u.id
      WHERE t.user_id = ?
    `);
    return stmt.get(user_id);
  }

  static findByTeacherId(teacherId) {
    const stmt = db.prepare(`
      SELECT t.*, u.name, u.email, u.phone
      FROM teachers t
      JOIN users u ON t.user_id = u.id
      WHERE t.teacher_id = ?
    `);
    return stmt.get(teacherId);
  }

  static getAll() {
    const stmt = db.prepare(`
      SELECT t.*, u.name, u.email, u.phone
      FROM teachers t
      JOIN users u ON t.user_id = u.id
      ORDER BY u.name
    `);
    return stmt.all();
  }

  static findAll(filters = {}) {
    let query = `
      SELECT t.*, u.name, u.email, u.phone
      FROM teachers t
      JOIN users u ON t.user_id = u.id
    `;
    let params = [];
    let conditions = [];

    if (filters.subject) {
      conditions.push('t.subject = ?');
      params.push(filters.subject);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY u.name';

    const stmt = db.prepare(query);
    return stmt.all(...params);
  }

  static update(id, teacherData) {
    const { teacher_id, subject, qualification, experience } = teacherData;

    const stmt = db.prepare(`
      UPDATE teachers
      SET teacher_id = ?, subject = ?, qualification = ?, experience = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    stmt.run(teacher_id, subject, qualification, experience, id);
    return this.findById(id);
  }

  static delete(id) {
    const stmt = db.prepare('DELETE FROM teachers WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static getClassStats(teacherId) {
    const stmt = db.prepare(`
      SELECT 
        COUNT(*) as total_classes,
        COUNT(DISTINCT grade) as grades_taught
      FROM classes 
      WHERE teacher_id = ?
    `);
    return stmt.get(teacherId);
  }
}

module.exports = Teacher;
