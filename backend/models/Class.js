const db = require('./database');

class Class {
  static create(classData) {
    const { name, subject, teacher_id, grade, section, schedule, virtual_link } = classData;
    
    const stmt = db.prepare(`
      INSERT INTO classes (name, subject, teacher_id, grade, section, schedule, virtual_link)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(name, subject, teacher_id, grade, section, schedule, virtual_link);
    return this.findById(result.lastInsertRowid);
  }

  static findById(id) {
    const stmt = db.prepare(`
      SELECT c.*, u.name as teacher_name
      FROM classes c
      LEFT JOIN teachers t ON c.teacher_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      WHERE c.id = ?
    `);
    return stmt.get(id);
  }

  static findAll(filters = {}) {
    let query = `
      SELECT c.*, u.name as teacher_name
      FROM classes c
      LEFT JOIN teachers t ON c.teacher_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
    `;
    let params = [];
    let conditions = [];

    if (filters.grade) {
      conditions.push('c.grade = ?');
      params.push(filters.grade);
    }

    if (filters.section) {
      conditions.push('c.section = ?');
      params.push(filters.section);
    }

    if (filters.teacher_id) {
      conditions.push('c.teacher_id = ?');
      params.push(filters.teacher_id);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY c.grade, c.section, c.name';
    
    const stmt = db.prepare(query);
    return stmt.all(...params);
  }

  static findByTeacher(teacher_id) {
    const stmt = db.prepare(`
      SELECT c.*, COUNT(s.id) as student_count
      FROM classes c
      LEFT JOIN students s ON c.grade = s.grade AND c.section = s.section
      WHERE c.teacher_id = ?
      GROUP BY c.id
      ORDER BY c.name
    `);
    return stmt.all(teacher_id);
  }

  static findByStudent(student_id) {
    const stmt = db.prepare(`
      SELECT c.*, u.name as teacher_name
      FROM classes c
      LEFT JOIN teachers t ON c.teacher_id = t.id
      LEFT JOIN users u ON t.user_id = u.id
      JOIN students s ON c.grade = s.grade AND c.section = s.section
      WHERE s.id = ?
      ORDER BY c.name
    `);
    return stmt.all(student_id);
  }

  static update(id, classData) {
    const { name, subject, teacher_id, grade, section, schedule, virtual_link } = classData;
    
    const stmt = db.prepare(`
      UPDATE classes 
      SET name = ?, subject = ?, teacher_id = ?, grade = ?, section = ?, schedule = ?, virtual_link = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(name, subject, teacher_id, grade, section, schedule, virtual_link, id);
    return this.findById(id);
  }

  static delete(id) {
    const stmt = db.prepare('DELETE FROM classes WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static getStudentCount(classId) {
    const stmt = db.prepare(`
      SELECT COUNT(*) as count
      FROM students s
      JOIN classes c ON s.grade = c.grade AND s.section = c.section
      WHERE c.id = ?
    `);
    return stmt.get(classId)?.count || 0;
  }
}

module.exports = Class;
