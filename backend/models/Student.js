const db = require('./database');

class Student {
  static create(studentData) {
    const { user_id, student_id, grade, section, parent_name, parent_phone, address } = studentData;
    
    const stmt = db.prepare(`
      INSERT INTO students (user_id, student_id, grade, section, parent_name, parent_phone, address)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(user_id, student_id, grade, section, parent_name, parent_phone, address);
    return this.findById(result.lastInsertRowid);
  }

  static findById(id) {
    const stmt = db.prepare(`
      SELECT s.*, u.name, u.email, u.phone
      FROM students s
      JOIN users u ON s.user_id = u.id
      WHERE s.id = ?
    `);
    return stmt.get(id);
  }

  static findByUserId(user_id) {
    const stmt = db.prepare(`
      SELECT s.*, u.name, u.email, u.phone
      FROM students s
      JOIN users u ON s.user_id = u.id
      WHERE s.user_id = ?
    `);
    return stmt.get(user_id);
  }

  static findByStudentId(studentId) {
    const stmt = db.prepare(`
      SELECT s.*, u.name, u.email, u.phone
      FROM students s
      JOIN users u ON s.user_id = u.id
      WHERE s.student_id = ?
    `);
    return stmt.get(studentId);
  }

  static findAll(filters = {}) {
    let query = `
      SELECT s.*, u.name, u.email, u.phone
      FROM students s
      JOIN users u ON s.user_id = u.id
    `;
    let params = [];
    let conditions = [];

    if (filters.grade) {
      conditions.push('s.grade = ?');
      params.push(filters.grade);
    }

    if (filters.section) {
      conditions.push('s.section = ?');
      params.push(filters.section);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY s.grade, s.section, u.name';
    
    const stmt = db.prepare(query);
    return stmt.all(...params);
  }

  static update(id, studentData) {
    const { student_id, grade, section, parent_name, parent_phone, address } = studentData;

    const stmt = db.prepare(`
      UPDATE students
      SET student_id = ?, grade = ?, section = ?, parent_name = ?, parent_phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    stmt.run(student_id, grade, section, parent_name, parent_phone, address, id);
    return this.findById(id);
  }

  static delete(id) {
    const stmt = db.prepare('DELETE FROM students WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static getAttendanceStats(studentId) {
    const stmt = db.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM attendance 
      WHERE student_id = ?
      GROUP BY status
    `);
    return stmt.all(studentId);
  }

  static getScoreStats(studentId) {
    const stmt = db.prepare(`
      SELECT 
        subject,
        AVG(score * 100.0 / max_score) as average_percentage,
        COUNT(*) as total_tests
      FROM scores 
      WHERE student_id = ?
      GROUP BY subject
    `);
    return stmt.all(studentId);
  }

  static getGradeStats() {
    const stmt = db.prepare(`
      SELECT 
        grade,
        section,
        COUNT(*) as student_count
      FROM students 
      GROUP BY grade, section
      ORDER BY grade, section
    `);
    return stmt.all();
  }
}

module.exports = Student;
