const db = require('./database');

class Score {
  static create(scoreData) {
    const { student_id, subject, score, max_score, test_date, type, description, teacher_id } = scoreData;
    
    const stmt = db.prepare(`
      INSERT INTO scores (student_id, subject, score, max_score, test_date, type, description, teacher_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(student_id, subject, score, max_score, test_date, type, description, teacher_id);
    return this.findById(result.lastInsertRowid);
  }

  static findById(id) {
    const stmt = db.prepare(`
      SELECT sc.*, s.student_id, u.name as student_name, tu.name as teacher_name
      FROM scores sc
      JOIN students s ON sc.student_id = s.id
      JOIN users u ON s.user_id = u.id
      LEFT JOIN teachers t ON sc.teacher_id = t.id
      LEFT JOIN users tu ON t.user_id = tu.id
      WHERE sc.id = ?
    `);
    return stmt.get(id);
  }

  static findByStudent(student_id, filters = {}) {
    let query = `
      SELECT sc.*, tu.name as teacher_name
      FROM scores sc
      LEFT JOIN teachers t ON sc.teacher_id = t.id
      LEFT JOIN users tu ON t.user_id = tu.id
      WHERE sc.student_id = ?
    `;
    let params = [student_id];

    if (filters.subject) {
      query += ' AND sc.subject = ?';
      params.push(filters.subject);
    }

    if (filters.type) {
      query += ' AND sc.type = ?';
      params.push(filters.type);
    }

    if (filters.startDate) {
      query += ' AND sc.test_date >= ?';
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      query += ' AND sc.test_date <= ?';
      params.push(filters.endDate);
    }

    query += ' ORDER BY sc.test_date DESC, sc.subject';
    
    const stmt = db.prepare(query);
    return stmt.all(...params);
  }

  static findByTeacher(teacher_id, filters = {}) {
    let query = `
      SELECT sc.*, s.student_id, u.name as student_name, s.grade, s.section
      FROM scores sc
      JOIN students s ON sc.student_id = s.id
      JOIN users u ON s.user_id = u.id
      WHERE sc.teacher_id = ?
    `;
    let params = [teacher_id];

    if (filters.subject) {
      query += ' AND sc.subject = ?';
      params.push(filters.subject);
    }

    if (filters.grade) {
      query += ' AND s.grade = ?';
      params.push(filters.grade);
    }

    if (filters.section) {
      query += ' AND s.section = ?';
      params.push(filters.section);
    }

    query += ' ORDER BY sc.test_date DESC, u.name';
    
    const stmt = db.prepare(query);
    return stmt.all(...params);
  }

  static update(id, scoreData) {
    const { score, max_score, test_date, type, description } = scoreData;
    
    const stmt = db.prepare(`
      UPDATE scores 
      SET score = ?, max_score = ?, test_date = ?, type = ?, description = ?
      WHERE id = ?
    `);
    
    stmt.run(score, max_score, test_date, type, description, id);
    return this.findById(id);
  }

  static delete(id) {
    const stmt = db.prepare('DELETE FROM scores WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static getSubjectAverage(student_id, subject) {
    const stmt = db.prepare(`
      SELECT 
        AVG(score * 100.0 / max_score) as average_percentage,
        COUNT(*) as total_tests
      FROM scores 
      WHERE student_id = ? AND subject = ?
    `);
    return stmt.get(student_id, subject);
  }

  static getClassAverage(subject, grade, section) {
    const stmt = db.prepare(`
      SELECT 
        AVG(sc.score * 100.0 / sc.max_score) as average_percentage,
        COUNT(*) as total_tests,
        COUNT(DISTINCT sc.student_id) as student_count
      FROM scores sc
      JOIN students s ON sc.student_id = s.id
      WHERE sc.subject = ? AND s.grade = ? AND s.section = ?
    `);
    return stmt.get(subject, grade, section);
  }

  static getTopPerformers(subject, grade, section, limit = 10) {
    const stmt = db.prepare(`
      SELECT 
        s.student_id,
        u.name as student_name,
        AVG(sc.score * 100.0 / sc.max_score) as average_percentage
      FROM scores sc
      JOIN students s ON sc.student_id = s.id
      JOIN users u ON s.user_id = u.id
      WHERE sc.subject = ? AND s.grade = ? AND s.section = ?
      GROUP BY sc.student_id, s.student_id, u.name
      ORDER BY average_percentage DESC
      LIMIT ?
    `);
    return stmt.all(subject, grade, section, limit);
  }
}

module.exports = Score;
