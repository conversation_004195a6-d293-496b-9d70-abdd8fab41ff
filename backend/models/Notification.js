const db = require('./database');

class Notification {
  static create(notificationData) {
    const { user_id, title, message, type, data, expires_at } = notificationData;
    
    const stmt = db.prepare(`
      INSERT INTO notifications (user_id, title, message, type, data, expires_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(user_id, title, message, type, JSON.stringify(data || {}), expires_at);
    return this.findById(result.lastInsertRowid);
  }

  static findById(id) {
    const stmt = db.prepare(`
      SELECT n.*, u.name as user_name
      FROM notifications n
      JOIN users u ON n.user_id = u.id
      WHERE n.id = ?
    `);
    const notification = stmt.get(id);
    if (notification && notification.data) {
      notification.data = JSON.parse(notification.data);
    }
    return notification;
  }

  static findByUser(user_id, filters = {}) {
    let query = `
      SELECT * FROM notifications
      WHERE user_id = ?
    `;
    let params = [user_id];

    if (filters.type) {
      query += ' AND type = ?';
      params.push(filters.type);
    }

    if (filters.unread_only) {
      query += ' AND read_at IS NULL';
    }

    // Filter out expired notifications
    query += ' AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)';

    query += ' ORDER BY created_at DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(filters.limit);
    }
    
    const stmt = db.prepare(query);
    const notifications = stmt.all(...params);
    
    return notifications.map(notification => {
      if (notification.data) {
        notification.data = JSON.parse(notification.data);
      }
      return notification;
    });
  }

  static markAsRead(id, user_id) {
    const stmt = db.prepare(`
      UPDATE notifications 
      SET read_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `);
    
    const result = stmt.run(id, user_id);
    return result.changes > 0;
  }

  static markAllAsRead(user_id) {
    const stmt = db.prepare(`
      UPDATE notifications 
      SET read_at = CURRENT_TIMESTAMP
      WHERE user_id = ? AND read_at IS NULL
    `);
    
    const result = stmt.run(user_id);
    return result.changes;
  }

  static delete(id, user_id) {
    const stmt = db.prepare('DELETE FROM notifications WHERE id = ? AND user_id = ?');
    const result = stmt.run(id, user_id);
    return result.changes > 0;
  }

  static deleteExpired() {
    const stmt = db.prepare(`
      DELETE FROM notifications 
      WHERE expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP
    `);
    const result = stmt.run();
    return result.changes;
  }

  static getUnreadCount(user_id) {
    const stmt = db.prepare(`
      SELECT COUNT(*) as count
      FROM notifications 
      WHERE user_id = ? AND read_at IS NULL 
      AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    `);
    return stmt.get(user_id)?.count || 0;
  }

  // Broadcast notification to multiple users
  static broadcast(user_ids, notificationData) {
    const { title, message, type, data, expires_at } = notificationData;
    const notifications = [];
    
    for (const user_id of user_ids) {
      const notification = this.create({
        user_id,
        title,
        message,
        type,
        data,
        expires_at
      });
      notifications.push(notification);
    }
    
    return notifications;
  }

  // Send notification to all users with specific role
  static broadcastToRole(role, notificationData) {
    const stmt = db.prepare('SELECT id FROM users WHERE role = ?');
    const users = stmt.all(role);
    const user_ids = users.map(user => user.id);
    
    return this.broadcast(user_ids, notificationData);
  }

  // Send notification to all students in a specific grade/section
  static broadcastToClass(grade, section, notificationData) {
    const stmt = db.prepare(`
      SELECT u.id FROM users u
      JOIN students s ON u.id = s.user_id
      WHERE s.grade = ? AND s.section = ?
    `);
    const users = stmt.all(grade, section);
    const user_ids = users.map(user => user.id);
    
    return this.broadcast(user_ids, notificationData);
  }
}

module.exports = Notification;
