const Score = require('../models/Score');
const Student = require('../models/Student');
const Teacher = require('../models/Teacher');

const createScore = async (req, res) => {
  try {
    const { student_id, subject, score, max_score, test_date, type, description } = req.body;
    const teacher_id = req.user.role === 'teacher' ? 
      Teacher.findByUserId(req.user.id)?.id : 
      req.body.teacher_id;

    const scoreData = Score.create({
      student_id,
      subject,
      score,
      max_score,
      test_date,
      type,
      description,
      teacher_id
    });

    res.status(201).json({
      message: 'Score added successfully',
      score: scoreData
    });

  } catch (error) {
    console.error('Create score error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getScoresByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subject, type, startDate, endDate } = req.query;

    // Check if user can access this student's data
    if (req.user.role === 'student') {
      const student = Student.findByUserId(req.user.id);
      if (!student || student.id.toString() !== studentId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const filters = {};
    if (subject) filters.subject = subject;
    if (type) filters.type = type;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const scores = Score.findByStudent(studentId, filters);
    
    res.json({ scores });

  } catch (error) {
    console.error('Get student scores error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getScoresByTeacher = async (req, res) => {
  try {
    const { teacherId } = req.params;
    const { subject, grade, section } = req.query;

    // Check if user can access this teacher's data
    if (req.user.role === 'teacher') {
      const teacher = Teacher.findByUserId(req.user.id);
      if (!teacher || teacher.id.toString() !== teacherId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const filters = {};
    if (subject) filters.subject = subject;
    if (grade) filters.grade = grade;
    if (section) filters.section = section;

    const scores = Score.findByTeacher(teacherId, filters);
    
    res.json({ scores });

  } catch (error) {
    console.error('Get teacher scores error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const updateScore = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const score = Score.findById(id);
    if (!score) {
      return res.status(404).json({ error: 'Score not found' });
    }

    // Check if teacher can update this score
    if (req.user.role === 'teacher') {
      const teacher = Teacher.findByUserId(req.user.id);
      if (!teacher || score.teacher_id !== teacher.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const updatedScore = Score.update(id, updateData);
    res.json({
      message: 'Score updated successfully',
      score: updatedScore
    });

  } catch (error) {
    console.error('Update score error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteScore = async (req, res) => {
  try {
    const { id } = req.params;

    const score = Score.findById(id);
    if (!score) {
      return res.status(404).json({ error: 'Score not found' });
    }

    // Check if teacher can delete this score
    if (req.user.role === 'teacher') {
      const teacher = Teacher.findByUserId(req.user.id);
      if (!teacher || score.teacher_id !== teacher.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const deleted = Score.delete(id);
    if (!deleted) {
      return res.status(400).json({ error: 'Failed to delete score' });
    }

    res.json({ message: 'Score deleted successfully' });

  } catch (error) {
    console.error('Delete score error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getScoreAnalytics = async (req, res) => {
  try {
    const { subject, grade, section } = req.query;

    if (!subject || !grade || !section) {
      return res.status(400).json({ error: 'Subject, grade, and section are required' });
    }

    const classAverage = Score.getClassAverage(subject, grade, section);
    const topPerformers = Score.getTopPerformers(subject, grade, section, 5);

    res.json({
      classAverage,
      topPerformers
    });

  } catch (error) {
    console.error('Get score analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  createScore,
  getScoresByStudent,
  getScoresByTeacher,
  updateScore,
  deleteScore,
  getScoreAnalytics
};
