const Notification = require('../models/Notification');

const getNotifications = async (req, res) => {
  try {
    const user_id = req.user.id;
    const { type, unread_only, limit } = req.query;

    const filters = {};
    if (type) filters.type = type;
    if (unread_only === 'true') filters.unread_only = true;
    if (limit) filters.limit = parseInt(limit);

    const notifications = Notification.findByUser(user_id, filters);
    const unreadCount = Notification.getUnreadCount(user_id);

    res.json({ 
      notifications,
      unreadCount
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    const success = Notification.markAsRead(id, user_id);
    
    if (!success) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json({ message: 'Notification marked as read' });

  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const markAllAsRead = async (req, res) => {
  try {
    const user_id = req.user.id;
    const count = Notification.markAllAsRead(user_id);

    res.json({ 
      message: 'All notifications marked as read',
      count
    });

  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    const success = Notification.delete(id, user_id);
    
    if (!success) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json({ message: 'Notification deleted' });

  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const createNotification = async (req, res) => {
  try {
    const { user_id, title, message, type, data, expires_at } = req.body;

    const notification = Notification.create({
      user_id,
      title,
      message,
      type,
      data,
      expires_at
    });

    res.status(201).json({
      message: 'Notification created successfully',
      notification
    });

  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const broadcastNotification = async (req, res) => {
  try {
    const { target_type, target_value, title, message, type, data, expires_at } = req.body;

    let notifications = [];

    if (target_type === 'role') {
      notifications = Notification.broadcastToRole(target_value, {
        title,
        message,
        type,
        data,
        expires_at
      });
    } else if (target_type === 'class') {
      const [grade, section] = target_value.split('-');
      notifications = Notification.broadcastToClass(grade, section, {
        title,
        message,
        type,
        data,
        expires_at
      });
    } else if (target_type === 'users' && Array.isArray(target_value)) {
      notifications = Notification.broadcast(target_value, {
        title,
        message,
        type,
        data,
        expires_at
      });
    } else {
      return res.status(400).json({ error: 'Invalid target type or value' });
    }

    res.status(201).json({
      message: 'Notifications sent successfully',
      count: notifications.length,
      notifications
    });

  } catch (error) {
    console.error('Broadcast notification error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const cleanupExpired = async (req, res) => {
  try {
    const count = Notification.deleteExpired();

    res.json({
      message: 'Expired notifications cleaned up',
      count
    });

  } catch (error) {
    console.error('Cleanup expired notifications error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createNotification,
  broadcastNotification,
  cleanupExpired
};
