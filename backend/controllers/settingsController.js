const fs = require('fs');
const path = require('path');

// Settings file path
const SETTINGS_FILE = path.join(__dirname, '../data/settings.json');

// Default settings
const DEFAULT_SETTINGS = {
  grades: ['9', '10', '11', '12'],
  sections: ['A', 'B', 'C'],
  subjects: [
    'Mathematics',
    'English',
    'Physics',
    'Chemistry',
    'Biology',
    'History',
    'Geography',
    'Computer Science',
    'Islamic Studies',
    'Arabic'
  ],
  school: {
    name: 'HIKMAAH',
    code: 'HS001',
    academic_year: '2024-2025',
    current_semester: '1'
  },
  system: {
    email_notifications: true,
    auto_backup: true,
    maintenance_mode: false
  }
};

// Ensure settings file exists
function ensureSettingsFile() {
  try {
    if (!fs.existsSync(SETTINGS_FILE)) {
      const dataDir = path.dirname(SETTINGS_FILE);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      fs.writeFileSync(SETTINGS_FILE, JSON.stringify(DEFAULT_SETTINGS, null, 2));
      console.log('✅ Settings file created with default values');
    }
  } catch (error) {
    console.error('Error ensuring settings file:', error);
  }
}

// Initialize settings on module load
ensureSettingsFile();

// Read settings from file
function readSettings() {
  try {
    ensureSettingsFile();
    const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading settings:', error);
    return DEFAULT_SETTINGS;
  }
}

// Write settings to file
function writeSettings(settings) {
  try {
    ensureSettingsFile();
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing settings:', error);
    return false;
  }
}

const settingsController = {
  // Get all settings
  getSettings: async (req, res) => {
    try {
      const settings = readSettings();
      res.json({
        message: 'Settings retrieved successfully',
        data: settings
      });
    } catch (error) {
      console.error('Get settings error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update settings
  updateSettings: async (req, res) => {
    try {
      const currentSettings = readSettings();
      const updatedSettings = {
        ...currentSettings,
        ...req.body
      };

      const success = writeSettings(updatedSettings);
      if (success) {
        res.json({
          message: 'Settings updated successfully',
          data: updatedSettings
        });
      } else {
        res.status(500).json({ error: 'Failed to save settings' });
      }
    } catch (error) {
      console.error('Update settings error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update grades
  updateGrades: async (req, res) => {
    try {
      const { grades } = req.body;
      
      if (!Array.isArray(grades) || grades.length === 0) {
        return res.status(400).json({ error: 'Grades must be a non-empty array' });
      }

      const currentSettings = readSettings();
      currentSettings.grades = grades;

      const success = writeSettings(currentSettings);
      if (success) {
        res.json({
          message: 'Grades updated successfully',
          data: currentSettings
        });
      } else {
        res.status(500).json({ error: 'Failed to save grades' });
      }
    } catch (error) {
      console.error('Update grades error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update sections
  updateSections: async (req, res) => {
    try {
      const { sections } = req.body;
      
      if (!Array.isArray(sections) || sections.length === 0) {
        return res.status(400).json({ error: 'Sections must be a non-empty array' });
      }

      const currentSettings = readSettings();
      currentSettings.sections = sections;

      const success = writeSettings(currentSettings);
      if (success) {
        res.json({
          message: 'Sections updated successfully',
          data: currentSettings
        });
      } else {
        res.status(500).json({ error: 'Failed to save sections' });
      }
    } catch (error) {
      console.error('Update sections error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get grades
  getGrades: async (req, res) => {
    try {
      const settings = readSettings();
      res.json({
        message: 'Grades retrieved successfully',
        data: settings.grades
      });
    } catch (error) {
      console.error('Get grades error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get sections
  getSections: async (req, res) => {
    try {
      const settings = readSettings();
      res.json({
        message: 'Sections retrieved successfully',
        data: settings.sections
      });
    } catch (error) {
      console.error('Get sections error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Update subjects
  updateSubjects: async (req, res) => {
    try {
      const { subjects } = req.body;

      if (!Array.isArray(subjects) || subjects.length === 0) {
        return res.status(400).json({ error: 'Subjects must be a non-empty array' });
      }

      const currentSettings = readSettings();
      currentSettings.subjects = subjects;

      const success = writeSettings(currentSettings);
      if (success) {
        res.json({
          message: 'Subjects updated successfully',
          data: currentSettings
        });
      } else {
        res.status(500).json({ error: 'Failed to save subjects' });
      }
    } catch (error) {
      console.error('Update subjects error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Get subjects
  getSubjects: async (req, res) => {
    try {
      const settings = readSettings();
      res.json({
        message: 'Subjects retrieved successfully',
        data: settings.subjects
      });
    } catch (error) {
      console.error('Get subjects error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = settingsController;
