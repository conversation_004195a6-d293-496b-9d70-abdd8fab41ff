const Attendance = require('../models/Attendance');
const Score = require('../models/Score');
const Student = require('../models/Student');
const User = require('../models/User');

const generateAttendanceReport = async (req, res) => {
  try {
    const { grade, section, startDate, endDate, format = 'json' } = req.query;

    const filters = {};
    if (grade) filters.grade = grade;
    if (section) filters.section = section;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const report = Attendance.getAttendanceReport(filters);

    // Calculate summary statistics
    const summary = {
      totalStudents: report.length,
      averageAttendance: report.length > 0 ? 
        Math.round(report.reduce((sum, student) => sum + (student.attendance_percentage || 0), 0) / report.length) : 0,
      perfectAttendance: report.filter(student => student.attendance_percentage === 100).length,
      belowThreshold: report.filter(student => student.attendance_percentage < 75).length
    };

    const reportData = {
      title: 'Attendance Report',
      filters,
      summary,
      data: report,
      generatedAt: new Date().toISOString(),
      generatedBy: req.user.name
    };

    if (format === 'csv') {
      return generateCSVResponse(res, reportData, 'attendance_report');
    }

    res.json(reportData);

  } catch (error) {
    console.error('Generate attendance report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const generateScoreReport = async (req, res) => {
  try {
    const { grade, section, subject, startDate, endDate, format = 'json' } = req.query;

    if (!subject) {
      return res.status(400).json({ error: 'Subject is required for score reports' });
    }

    // Get class average and top performers
    const classAverage = Score.getClassAverage(subject, grade, section);
    const topPerformers = Score.getTopPerformers(subject, grade, section, 10);

    // Get all students in the class with their scores
    const students = Student.findAll({ grade, section });
    const studentScores = students.map(student => {
      const scores = Score.findByStudent(student.id, { subject, startDate, endDate });
      const average = scores.length > 0 ? 
        Math.round(scores.reduce((sum, score) => sum + (score.score * 100 / score.max_score), 0) / scores.length) : 0;
      
      return {
        student_id: student.student_id,
        student_name: student.name,
        grade: student.grade,
        section: student.section,
        average_percentage: average,
        total_tests: scores.length,
        scores: scores
      };
    });

    const summary = {
      totalStudents: students.length,
      classAverage: Math.round(classAverage?.average_percentage || 0),
      totalTests: classAverage?.total_tests || 0,
      topPerformer: topPerformers[0] || null,
      aboveAverage: studentScores.filter(s => s.average_percentage > (classAverage?.average_percentage || 0)).length
    };

    const reportData = {
      title: 'Score Report',
      subject,
      filters: { grade, section, startDate, endDate },
      summary,
      data: studentScores,
      topPerformers,
      generatedAt: new Date().toISOString(),
      generatedBy: req.user.name
    };

    if (format === 'csv') {
      return generateCSVResponse(res, reportData, 'score_report');
    }

    res.json(reportData);

  } catch (error) {
    console.error('Generate score report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const generateStudentReport = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { startDate, endDate, format = 'json' } = req.query;

    const student = Student.findById(studentId);
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Check access permissions
    if (req.user.role === 'student') {
      const userStudent = Student.findByUserId(req.user.id);
      if (!userStudent || userStudent.id.toString() !== studentId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Get attendance data
    const attendanceFilters = {};
    if (startDate) attendanceFilters.startDate = startDate;
    if (endDate) attendanceFilters.endDate = endDate;
    
    const attendance = Attendance.findByStudent(studentId, attendanceFilters);
    const attendanceStats = Student.getAttendanceStats(studentId);

    // Get score data
    const scores = Score.findByStudent(studentId, attendanceFilters);
    const scoreStats = Student.getScoreStats(studentId);

    // Calculate summary
    const totalDays = attendance.length;
    const presentDays = attendance.filter(a => a.status === 'present').length;
    const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;

    const summary = {
      attendancePercentage,
      totalDays,
      presentDays,
      absentDays: attendance.filter(a => a.status === 'absent').length,
      lateDays: attendance.filter(a => a.status === 'late').length,
      totalTests: scores.length,
      averageScore: scores.length > 0 ? 
        Math.round(scores.reduce((sum, score) => sum + (score.score * 100 / score.max_score), 0) / scores.length) : 0
    };

    const reportData = {
      title: 'Student Report',
      student,
      filters: { startDate, endDate },
      summary,
      attendance,
      attendanceStats,
      scores,
      scoreStats,
      generatedAt: new Date().toISOString(),
      generatedBy: req.user.name
    };

    if (format === 'csv') {
      return generateCSVResponse(res, reportData, `student_report_${student.student_id}`);
    }

    res.json(reportData);

  } catch (error) {
    console.error('Generate student report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const generateOverviewReport = async (req, res) => {
  try {
    const userStats = User.getStats();
    const gradeStats = Student.getGradeStats();

    // Get recent activity summary
    const recentAttendance = Attendance.findByClass(null, { 
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] 
    });

    const summary = {
      totalUsers: userStats.reduce((sum, stat) => sum + stat.count, 0),
      totalStudents: userStats.find(s => s.role === 'student')?.count || 0,
      totalTeachers: userStats.find(s => s.role === 'teacher')?.count || 0,
      totalClasses: gradeStats.length,
      recentAttendanceRecords: recentAttendance.length
    };

    const reportData = {
      title: 'System Overview Report',
      summary,
      userStats,
      gradeStats,
      generatedAt: new Date().toISOString(),
      generatedBy: req.user.name
    };

    res.json(reportData);

  } catch (error) {
    console.error('Generate overview report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Helper function to generate CSV response
const generateCSVResponse = (res, data, filename) => {
  try {
    let csv = '';
    
    if (data.data && Array.isArray(data.data)) {
      // Generate CSV headers
      const headers = Object.keys(data.data[0] || {});
      csv += headers.join(',') + '\n';
      
      // Generate CSV rows
      data.data.forEach(row => {
        const values = headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        });
        csv += values.join(',') + '\n';
      });
    }

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}_${new Date().toISOString().split('T')[0]}.csv"`);
    res.send(csv);

  } catch (error) {
    console.error('Generate CSV error:', error);
    res.status(500).json({ error: 'Failed to generate CSV' });
  }
};

module.exports = {
  generateAttendanceReport,
  generateScoreReport,
  generateStudentReport,
  generateOverviewReport
};
