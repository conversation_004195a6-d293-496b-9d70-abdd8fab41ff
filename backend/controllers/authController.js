const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Student = require('../models/Student');

const generateToken = (user) => {
  return jwt.sign(
    { 
      id: user.id, 
      email: user.email, 
      role: user.role,
      name: user.name 
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = User.findByEmail(email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    // Validate password
    const isValidPassword = User.validatePassword(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    // Generate token
    const token = generateToken(user);

    // Get additional user data based on role
    let userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name,
      phone: user.phone
    };

    if (user.role === 'student') {
      const studentData = Student.findByUserId(user.id);
      if (studentData) {
        userData.student = {
          id: studentData.id,
          student_id: studentData.student_id,
          grade: studentData.grade,
          section: studentData.section,
          parent_name: studentData.parent_name,
          parent_phone: studentData.parent_phone,
          address: studentData.address
        };
      }
    } else if (user.role === 'teacher') {
      const Teacher = require('../models/Teacher');
      const teacherData = Teacher.findByUserId(user.id);
      if (teacherData) {
        userData.teacher = {
          id: teacherData.id,
          teacher_id: teacherData.teacher_id,
          subject: teacherData.subject,
          qualification: teacherData.qualification,
          experience: teacherData.experience
        };
      }
    }

    res.json({
      message: 'Login successful',
      token,
      user: userData
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getMe = async (req, res) => {
  try {
    const user = User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    let userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name,
      phone: user.phone
    };

    if (user.role === 'student') {
      const studentData = Student.findByUserId(user.id);
      if (studentData) {
        userData.student = {
          id: studentData.id,
          student_id: studentData.student_id,
          grade: studentData.grade,
          section: studentData.section,
          parent_name: studentData.parent_name,
          parent_phone: studentData.parent_phone,
          address: studentData.address
        };
      }
    } else if (user.role === 'teacher') {
      const Teacher = require('../models/Teacher');
      const teacherData = Teacher.findByUserId(user.id);
      if (teacherData) {
        userData.teacher = {
          id: teacherData.id,
          teacher_id: teacherData.teacher_id,
          subject: teacherData.subject,
          qualification: teacherData.qualification,
          experience: teacherData.experience
        };
      }
    }

    res.json({ user: userData });

  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const logout = async (req, res) => {
  // Since we're using stateless JWT, logout is handled on the client side
  // by removing the token from storage
  res.json({ message: 'Logout successful' });
};

const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Get current user
    const user = User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Validate current password
    const isValidPassword = User.validatePassword(currentPassword, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Update password
    User.updatePassword(userId, newPassword);

    res.json({ message: 'Password updated successfully' });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  login,
  getMe,
  logout,
  changePassword
};
