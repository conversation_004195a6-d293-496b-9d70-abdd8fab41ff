const express = require('express');
const router = express.Router();
const scoreController = require('../controllers/scoreController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Get score analytics
router.get('/analytics', scoreController.getScoreAnalytics);

// Get scores by student
router.get('/student/:studentId', scoreController.getScoresByStudent);

// Get scores by teacher
router.get('/teacher/:teacherId', authorizeRoles('admin', 'teacher'), scoreController.getScoresByTeacher);

// Create new score (teachers and admin only)
router.post('/', authorizeRoles('admin', 'teacher'), validate(schemas.createScore), scoreController.createScore);

// Update score (teachers and admin only)
router.put('/:id', authorizeRoles('admin', 'teacher'), scoreController.updateScore);

// Delete score (teachers and admin only)
router.delete('/:id', authorizeRoles('admin', 'teacher'), scoreController.deleteScore);

module.exports = router;
