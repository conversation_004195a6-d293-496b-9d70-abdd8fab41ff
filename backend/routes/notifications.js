const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// Get user's notifications
router.get('/', notificationController.getNotifications);

// Mark notification as read
router.patch('/:id/read', notificationController.markAsRead);

// Mark all notifications as read
router.patch('/read-all', notificationController.markAllAsRead);

// Delete notification
router.delete('/:id', notificationController.deleteNotification);

// Create notification (admin and teachers only)
router.post('/', authorizeRoles('admin', 'teacher'), notificationController.createNotification);

// Broadcast notification (admin only)
router.post('/broadcast', authorizeRoles('admin'), notificationController.broadcastNotification);

// Cleanup expired notifications (admin only)
router.delete('/cleanup/expired', authorizeRoles('admin'), notificationController.cleanupExpired);

module.exports = router;
