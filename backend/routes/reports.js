const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// Generate attendance report (admin and teachers only)
router.get('/attendance', authorizeRoles('admin', 'teacher'), reportController.generateAttendanceReport);

// Generate score report (admin and teachers only)
router.get('/scores', authorizeRoles('admin', 'teacher'), reportController.generateScoreReport);

// Generate individual student report
router.get('/student/:studentId', reportController.generateStudentReport);

// Generate system overview report (admin only)
router.get('/overview', authorizeRoles('admin'), reportController.generateOverviewReport);

module.exports = router;
