const express = require('express');
const router = express.Router();
const teacherController = require('../controllers/teacherController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Get all teachers (admin and teachers can see all)
router.get('/', authorizeRoles('admin', 'teacher'), teacherController.getAllTeachers);

// Get teacher stats (admin only)
router.get('/stats', authorizeRoles('admin'), teacherController.getTeacherStats);

// Get specific teacher
router.get('/:id', teacherController.getTeacherById);

// Create new teacher (admin only)
router.post('/', authorizeRoles('admin'), validate(schemas.createTeacher), teacherController.createTeacher);

// Bulk create teachers (admin only)
router.post('/bulk', authorizeRoles('admin'), teacherController.bulkCreateTeachers);

// Export teachers (admin only)
router.get('/export', authorizeRoles('admin'), teacherController.exportTeachers);

// Update teacher (admin only, or teacher updating their own profile)
router.put('/:id', (req, res, next) => {
  if (req.user.role === 'teacher') {
    const teacher = require('../models/Teacher').findByUserId(req.user.id);
    if (!teacher || teacher.id.toString() !== req.params.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
  }
  next();
}, validate(schemas.updateTeacher), teacherController.updateTeacher);

// Delete teacher (admin only)
router.delete('/:id', authorizeRoles('admin'), teacherController.deleteTeacher);

module.exports = router;
