const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { upload, handleUploadError } = require('../middleware/upload');

// All routes require authentication
router.use(authenticateToken);

// Upload single file
router.post('/single', upload.single('file'), handleUploadError, uploadController.uploadFile);

// Upload profile picture
router.post('/profile', upload.single('profilePicture'), handleUploadError, uploadController.uploadFile);

// Upload document
router.post('/document', upload.single('document'), handleUploadError, uploadController.uploadFile);

// Upload report
router.post('/report', authorizeRoles('admin', 'teacher'), upload.single('report'), handleUploadError, uploadController.uploadFile);

// Upload multiple files
router.post('/multiple', upload.array('files', 5), handleUploadError, uploadController.uploadMultipleFiles);

// Get file
router.get('/:type/:filename', uploadController.getFile);

// Delete file (admin and teachers only)
router.delete('/:filename', authorizeRoles('admin', 'teacher'), uploadController.deleteFile);

module.exports = router;
