const express = require('express');
const router = express.Router();
const studentController = require('../controllers/studentController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Get all students (admin and teachers can see all, students see only themselves)
router.get('/', (req, res, next) => {
  if (req.user.role === 'student') {
    // Students can only see their own data
    req.params.id = req.user.id;
    return studentController.getStudentById(req, res);
  }
  next();
}, authorizeRoles('admin', 'teacher'), studentController.getAllStudents);

// Get student stats (admin only)
router.get('/stats', authorizeRoles('admin'), studentController.getStudentStats);

// Get specific student
router.get('/:id', (req, res, next) => {
  if (req.user.role === 'student') {
    // Students can only access their own data
    const student = require('../models/Student').findByUserId(req.user.id);
    if (!student || student.id.toString() !== req.params.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
  }
  next();
}, studentController.getStudentById);

// Create new student (admin only)
router.post('/', authorizeRoles('admin'), validate(schemas.createStudent), studentController.createStudent);

// Bulk create students (admin only)
router.post('/bulk', authorizeRoles('admin'), studentController.bulkCreateStudents);

// Export students (admin only)
router.get('/export', authorizeRoles('admin'), studentController.exportStudents);

// Update student (admin only, or student updating their own profile)
router.put('/:id', (req, res, next) => {
  if (req.user.role === 'student') {
    const student = require('../models/Student').findByUserId(req.user.id);
    if (!student || student.id.toString() !== req.params.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
  }
  next();
}, validate(schemas.updateStudent), studentController.updateStudent);

// Delete student (admin only)
router.delete('/:id', authorizeRoles('admin'), studentController.deleteStudent);

module.exports = router;
