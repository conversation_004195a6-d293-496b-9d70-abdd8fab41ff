const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Apply authentication to all routes
router.use(authenticateToken);

// Get all settings (admin only)
router.get('/', authorizeRoles('admin'), settingsController.getSettings);

// Update settings (admin only)
router.put('/', authorizeRoles('admin'), settingsController.updateSettings);

// Get grades (all authenticated users)
router.get('/grades', settingsController.getGrades);

// Update grades (admin only)
router.put('/grades', authorizeRoles('admin'), settingsController.updateGrades);

// Get sections (all authenticated users)
router.get('/sections', settingsController.getSections);

// Update sections (admin only)
router.put('/sections', authorizeRoles('admin'), settingsController.updateSections);

// Get subjects (all authenticated users)
router.get('/subjects', settingsController.getSubjects);

// Update subjects (admin only)
router.put('/subjects', authorizeRoles('admin'), settingsController.updateSubjects);

module.exports = router;
