{"name": "student-management-backend", "version": "1.0.0", "description": "Backend API for student management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^9.2.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.1.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "sqlite", "jwt", "student-management"], "author": "Student Management System", "license": "MIT"}