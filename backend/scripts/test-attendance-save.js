const axios = require('axios');

async function testAttendanceSave() {
  console.log('🧪 TESTING ATTENDANCE SAVE FUNCTIONALITY\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Login as teacher
    console.log('1️⃣ Teacher Login...');
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });
    
    const teacherToken = teacherLoginResponse.data.token;
    const teacherUser = teacherLoginResponse.data.user;
    const teacherAuthHeaders = {
      headers: { 'Authorization': `Bearer ${teacherToken}` }
    };
    
    console.log(`   ✅ Teacher logged in: ${teacherUser.name}`);
    console.log(`   📋 Teacher ID: ${teacherUser.teacher.id}`);

    // Get teacher's classes
    console.log('\n2️⃣ Getting Teacher Classes...');
    const classesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherUser.teacher.id}`, teacherAuthHeaders);
    const classes = classesResponse.data.classes;
    console.log(`   ✅ Found ${classes.length} classes`);
    
    if (classes.length === 0) {
      console.log('   ❌ No classes found for teacher');
      return;
    }

    const testClass = classes[0];
    console.log(`   📚 Using class: ${testClass.name} (Grade ${testClass.grade}${testClass.section})`);

    // Get students in the class
    console.log('\n3️⃣ Getting Students...');
    const studentsResponse = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherAuthHeaders);
    const students = studentsResponse.data.students;
    console.log(`   ✅ Found ${students.length} students`);
    
    if (students.length === 0) {
      console.log('   ❌ No students found in class');
      return;
    }

    // Test attendance marking
    console.log('\n4️⃣ Marking Attendance...');
    const testDate = '2025-06-24';
    const testStudent = students[0];
    
    const attendanceData = {
      student_id: testStudent.id,
      class_id: testClass.id,
      date: testDate,
      status: 'present',
      notes: 'Test attendance save'
    };

    console.log(`   📝 Marking ${testStudent.name} as present for ${testDate}`);
    const markResponse = await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherAuthHeaders);
    console.log('   ✅ Attendance marked successfully');

    // Verify attendance was saved
    console.log('\n5️⃣ Verifying Attendance Save...');
    const verifyResponse = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=${testDate}`, teacherAuthHeaders);
    const attendanceRecords = verifyResponse.data.attendance;
    
    console.log(`   📊 Found ${attendanceRecords.length} attendance records for ${testDate}`);
    
    // The attendance records return student_id as the student's ID string (like 'STU001'), not the database ID
    const studentRecord = attendanceRecords.find(record => record.student_id === testStudent.student_id);
    if (studentRecord) {
      console.log(`   ✅ Student record found: ${studentRecord.student_name} - ${studentRecord.status}`);
      console.log(`   📋 Notes: ${studentRecord.notes || 'None'}`);
      console.log(`   📋 Student ID: ${studentRecord.student_id}`);
    } else {
      console.log('   ❌ Student record NOT found - attendance not saved properly');
      console.log(`   🔍 Looking for student_id: ${testStudent.student_id}`);
      console.log(`   🔍 Available student_ids: ${attendanceRecords.map(r => r.student_id).join(', ')}`);
      return;
    }

    // Test updating attendance
    console.log('\n6️⃣ Testing Attendance Update...');
    const updateData = {
      student_id: testStudent.id,
      class_id: testClass.id,
      date: testDate,
      status: 'late',
      notes: 'Updated to late'
    };

    await axios.post(`${baseURL}/attendance/mark`, updateData, teacherAuthHeaders);
    console.log('   ✅ Attendance updated to late');

    // Verify update
    const verifyUpdateResponse = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=${testDate}`, teacherAuthHeaders);
    const updatedRecords = verifyUpdateResponse.data.attendance;
    const updatedRecord = updatedRecords.find(record => record.student_id === testStudent.student_id);

    if (updatedRecord && updatedRecord.status === 'late') {
      console.log('   ✅ Attendance update verified');
    } else {
      console.log('   ❌ Attendance update failed');
      console.log(`   🔍 Expected status: late, Got: ${updatedRecord?.status || 'not found'}`);
    }

    // Test multiple students
    console.log('\n7️⃣ Testing Multiple Students...');
    if (students.length > 1) {
      const multipleAttendance = students.slice(0, Math.min(3, students.length)).map((student, index) => ({
        student_id: student.id,
        class_id: testClass.id,
        date: testDate,
        status: ['present', 'absent', 'late'][index % 3],
        notes: `Bulk test ${index + 1}`
      }));

      for (const attendance of multipleAttendance) {
        await axios.post(`${baseURL}/attendance/mark`, attendance, teacherAuthHeaders);
      }
      
      console.log(`   ✅ Marked attendance for ${multipleAttendance.length} students`);

      // Verify all records
      const finalVerifyResponse = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=${testDate}`, teacherAuthHeaders);
      const finalRecords = finalVerifyResponse.data.attendance;
      console.log(`   📊 Final verification: ${finalRecords.length} total records`);
    }

    console.log('\n🎉 ATTENDANCE SAVE TEST COMPLETED SUCCESSFULLY!');
    console.log('\n📋 SUMMARY:');
    console.log('   ✅ Attendance marking works');
    console.log('   ✅ Attendance is properly saved to database');
    console.log('   ✅ Attendance can be retrieved after saving');
    console.log('   ✅ Attendance updates work correctly');
    console.log('   ✅ Multiple student attendance works');

  } catch (error) {
    console.error('\n❌ ATTENDANCE TEST FAILED:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testAttendanceSave();
