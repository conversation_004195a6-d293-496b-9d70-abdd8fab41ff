const axios = require('axios');

/**
 * Final comprehensive test to verify all issues are resolved
 */
async function finalTest() {
  console.log('🎯 FINAL COMPREHENSIVE TEST\n');
  console.log('Testing all reported issues and fixes...\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Issue 1: Class editing was failing with 400 error
    console.log('📝 Issue 1: Class Editing...');
    const adminLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLoginResponse.data.token;
    const adminAuthHeaders = { headers: { Authorization: `Bearer ${adminToken}` } };

    // Get a class to edit
    const classesResponse = await axios.get(`${baseURL}/classes`, adminAuthHeaders);
    const testClass = classesResponse.data.classes[0];

    const updateData = {
      name: testClass.name + ' (Test Update)',
      subject: testClass.subject,
      teacher_id: testClass.teacher_id,
      grade: testClass.grade, // Keep as string - this was the fix
      section: testClass.section,
      schedule: testClass.schedule
    };

    const updateResponse = await axios.put(`${baseURL}/classes/${testClass.id}`, updateData, adminAuthHeaders);
    console.log('   ✅ Class editing now works correctly');

    // Issue 2: Teacher attendance dropdown was empty
    console.log('\n👩‍🏫 Issue 2: Teacher Attendance System...');
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });

    const teacherToken = teacherLoginResponse.data.token;
    const teacherId = teacherLoginResponse.data.user.teacher.id; // Now available from auth
    const teacherAuthHeaders = { headers: { Authorization: `Bearer ${teacherToken}` } };

    const teacherClassesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherId}`, teacherAuthHeaders);
    const teacherClasses = teacherClassesResponse.data.classes;
    
    console.log(`   ✅ Teacher can see ${teacherClasses.length} assigned classes`);
    console.log(`   ✅ Teacher profile data included in login response`);

    // Issue 3: Settings not saving properly
    console.log('\n⚙️ Issue 3: Admin Settings Management...');
    
    // Test settings API
    const settingsResponse = await axios.get(`${baseURL}/settings`, adminAuthHeaders);
    const originalGrades = settingsResponse.data.data.grades;
    const originalSections = settingsResponse.data.data.sections;
    
    console.log(`   📊 Current grades: ${originalGrades.join(', ')}`);
    console.log(`   📊 Current sections: ${originalSections.join(', ')}`);

    // Test adding a grade
    const testGrades = [...originalGrades, 'TestGrade'];
    await axios.put(`${baseURL}/settings/grades`, { grades: testGrades }, adminAuthHeaders);
    
    // Verify it was saved
    const updatedSettingsResponse = await axios.get(`${baseURL}/settings`, adminAuthHeaders);
    const newGrades = updatedSettingsResponse.data.data.grades;
    
    if (newGrades.includes('TestGrade')) {
      console.log('   ✅ Grade addition works correctly');
    } else {
      throw new Error('Grade was not saved');
    }

    // Test adding a section
    const testSections = [...originalSections, 'TestSection'];
    await axios.put(`${baseURL}/settings/sections`, { sections: testSections }, adminAuthHeaders);
    
    // Verify it was saved
    const updatedSectionsResponse = await axios.get(`${baseURL}/settings`, adminAuthHeaders);
    const newSections = updatedSectionsResponse.data.data.sections;
    
    if (newSections.includes('TestSection')) {
      console.log('   ✅ Section addition works correctly');
    } else {
      throw new Error('Section was not saved');
    }

    // Restore original settings
    await axios.put(`${baseURL}/settings/grades`, { grades: originalGrades }, adminAuthHeaders);
    await axios.put(`${baseURL}/settings/sections`, { sections: originalSections }, adminAuthHeaders);
    console.log('   ✅ Settings properly restored');

    // Issue 4: Student pages not accessible
    console.log('\n🎓 Issue 4: Student Page Access...');
    const studentLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'student123'
    });

    const studentToken = studentLoginResponse.data.token;
    const studentId = studentLoginResponse.data.user.student.id;
    const studentAuthHeaders = { headers: { Authorization: `Bearer ${studentToken}` } };

    // Test student attendance page
    const studentAttendanceResponse = await axios.get(`${baseURL}/attendance/student/${studentId}`, studentAuthHeaders);
    console.log(`   ✅ Student attendance page accessible (${studentAttendanceResponse.data.attendance.length} records)`);

    // Test student scores page
    const studentScoresResponse = await axios.get(`${baseURL}/scores/student/${studentId}`, studentAuthHeaders);
    console.log(`   ✅ Student scores page accessible (${studentScoresResponse.data.scores.length} scores)`);

    // Test student classes page
    const studentClassesResponse = await axios.get(`${baseURL}/classes/student/${studentId}`, studentAuthHeaders);
    console.log(`   ✅ Student classes page accessible (${studentClassesResponse.data.classes.length} classes)`);

    // Bonus: Test attendance marking workflow
    console.log('\n📋 Bonus: Complete Attendance Workflow...');
    
    const testClassId = teacherClasses[0].id;
    const today = new Date().toISOString().split('T')[0];
    
    // Get students for the class
    const studentsResponse = await axios.get(`${baseURL}/students?grade=${teacherClasses[0].grade}&section=${teacherClasses[0].section}`, teacherAuthHeaders);
    const students = studentsResponse.data.students;
    
    if (students.length > 0) {
      // Mark attendance for first student
      const attendanceData = {
        student_id: students[0].id,
        class_id: testClassId,
        date: today,
        status: 'present',
        notes: 'Final test attendance'
      };

      await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherAuthHeaders);
      console.log('   ✅ Attendance marking workflow complete');
    }

    console.log('\n🎉 ALL ISSUES RESOLVED SUCCESSFULLY!');
    console.log('\n📋 FINAL STATUS:');
    console.log('   ✅ Class editing validation: FIXED');
    console.log('   ✅ Teacher attendance dropdown: FIXED');
    console.log('   ✅ Settings persistence: FIXED');
    console.log('   ✅ Student page access: FIXED');
    console.log('   ✅ Dynamic grades/sections: IMPLEMENTED');
    console.log('   ✅ Complete attendance workflow: WORKING');
    console.log('\n🚀 SYSTEM IS PRODUCTION READY!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
    throw error;
  }
}

// Run test if called directly
if (require.main === module) {
  finalTest()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = finalTest;
