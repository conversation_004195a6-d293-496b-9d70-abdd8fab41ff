const axios = require('axios');
const User = require('../models/User');
const Student = require('../models/Student');
const Teacher = require('../models/Teacher');

/**
 * Test script to verify API login functionality for newly created accounts
 */
async function testAPILogin() {
  console.log('🧪 Testing API login functionality...\n');

  const baseURL = 'http://localhost:3001/api';
  
  // Admin credentials for creating test accounts
  const adminCredentials = {
    email: '<EMAIL>',
    password: 'admin123'
  };

  try {
    // Step 1: Login as admin to get token
    console.log('🔐 Step 1: Logging in as admin...');
    const adminLoginResponse = await axios.post(`${baseURL}/auth/login`, adminCredentials);
    const adminToken = adminLoginResponse.data.token;
    console.log('   ✅ Admin login successful');

    const authHeaders = {
      headers: { Authorization: `Bearer ${adminToken}` }
    };

    // Step 2: Create a test student via API
    console.log('\n📚 Step 2: Creating test student via API...');
    const testStudentData = {
      name: 'API Test Student',
      email: '<EMAIL>',
      password: 'testpass123',
      student_id: 'API001',
      grade: '10',
      section: 'A',
      phone: '******-9999',
      parent_name: 'Test Parent',
      parent_phone: '******-8888',
      address: 'Test Address'
    };

    // Clean up existing test student if exists
    const existingStudentUser = User.findByEmail(testStudentData.email);
    if (existingStudentUser) {
      const existingStudent = Student.findByUserId(existingStudentUser.id);
      if (existingStudent) {
        await axios.delete(`${baseURL}/students/${existingStudent.id}`, authHeaders);
      }
      console.log('   Cleaned up existing test student');
    }

    const createStudentResponse = await axios.post(`${baseURL}/students`, testStudentData, authHeaders);
    console.log('   ✅ Student created via API');

    // Step 3: Test student login
    console.log('\n🎓 Step 3: Testing student login...');
    const studentLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: testStudentData.email,
      password: testStudentData.password
    });

    if (studentLoginResponse.data.token && studentLoginResponse.data.user.role === 'student') {
      console.log('   ✅ Student login successful');
      console.log(`   📋 Student info: ${studentLoginResponse.data.user.name} (${studentLoginResponse.data.user.email})`);
    } else {
      throw new Error('Student login failed - no token or wrong role');
    }

    // Step 4: Test student dashboard access
    console.log('\n📊 Step 4: Testing student dashboard access...');
    const studentToken = studentLoginResponse.data.token;
    const studentAuthHeaders = {
      headers: { Authorization: `Bearer ${studentToken}` }
    };

    const studentMeResponse = await axios.get(`${baseURL}/auth/me`, studentAuthHeaders);
    if (studentMeResponse.data.user.role === 'student') {
      console.log('   ✅ Student dashboard access successful');
    } else {
      throw new Error('Student dashboard access failed');
    }

    // Step 5: Create a test teacher via API
    console.log('\n👩‍🏫 Step 5: Creating test teacher via API...');
    const testTeacherData = {
      name: 'API Test Teacher',
      email: '<EMAIL>',
      password: 'teacherpass123',
      teacher_id: 'API001',
      subject: 'Test Subject',
      phone: '******-7777',
      qualification: 'Test Qualification',
      experience: 5
    };

    // Clean up existing test teacher if exists
    const existingTeacherUser = User.findByEmail(testTeacherData.email);
    if (existingTeacherUser) {
      const existingTeacher = Teacher.findByUserId(existingTeacherUser.id);
      if (existingTeacher) {
        await axios.delete(`${baseURL}/teachers/${existingTeacher.id}`, authHeaders);
      }
      console.log('   Cleaned up existing test teacher');
    }

    const createTeacherResponse = await axios.post(`${baseURL}/teachers`, testTeacherData, authHeaders);
    console.log('   ✅ Teacher created via API');

    // Step 6: Test teacher login
    console.log('\n🏫 Step 6: Testing teacher login...');
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: testTeacherData.email,
      password: testTeacherData.password
    });

    if (teacherLoginResponse.data.token && teacherLoginResponse.data.user.role === 'teacher') {
      console.log('   ✅ Teacher login successful');
      console.log(`   📋 Teacher info: ${teacherLoginResponse.data.user.name} (${teacherLoginResponse.data.user.email})`);
    } else {
      throw new Error('Teacher login failed - no token or wrong role');
    }

    // Step 7: Test teacher dashboard access
    console.log('\n📊 Step 7: Testing teacher dashboard access...');
    const teacherToken = teacherLoginResponse.data.token;
    const teacherAuthHeaders = {
      headers: { Authorization: `Bearer ${teacherToken}` }
    };

    const teacherMeResponse = await axios.get(`${baseURL}/auth/me`, teacherAuthHeaders);
    if (teacherMeResponse.data.user.role === 'teacher') {
      console.log('   ✅ Teacher dashboard access successful');
    } else {
      throw new Error('Teacher dashboard access failed');
    }

    // Step 8: Test bulk creation
    console.log('\n📦 Step 8: Testing bulk student creation...');
    const bulkStudentsData = {
      students: [
        {
          name: 'Bulk Student 1',
          email: '<EMAIL>',
          student_id: 'BULK001',
          grade: '10',
          section: 'A'
        },
        {
          name: 'Bulk Student 2',
          email: '<EMAIL>',
          student_id: 'BULK002',
          grade: '10',
          section: 'B'
        }
      ]
    };

    const bulkCreateResponse = await axios.post(`${baseURL}/students/bulk`, bulkStudentsData, authHeaders);
    console.log(`   ✅ Bulk creation: ${bulkCreateResponse.data.results.success.length} successful, ${bulkCreateResponse.data.results.failed.length} failed`);

    // Test login for bulk created students
    for (const successResult of bulkCreateResponse.data.results.success) {
      const bulkLoginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: successResult.email,
        password: successResult.password
      });
      
      if (bulkLoginResponse.data.token) {
        console.log(`   ✅ Bulk student login successful: ${successResult.email}`);
      }
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    
    // Clean up individual test accounts
    const testStudentUser = User.findByEmail(testStudentData.email);
    if (testStudentUser) {
      const testStudent = Student.findByUserId(testStudentUser.id);
      if (testStudent) {
        await axios.delete(`${baseURL}/students/${testStudent.id}`, authHeaders);
      }
    }

    const testTeacherUser = User.findByEmail(testTeacherData.email);
    if (testTeacherUser) {
      const testTeacher = Teacher.findByUserId(testTeacherUser.id);
      if (testTeacher) {
        await axios.delete(`${baseURL}/teachers/${testTeacher.id}`, authHeaders);
      }
    }

    // Clean up bulk created students
    for (const email of ['<EMAIL>', '<EMAIL>']) {
      const bulkUser = User.findByEmail(email);
      if (bulkUser) {
        const bulkStudent = Student.findByUserId(bulkUser.id);
        if (bulkStudent) {
          await axios.delete(`${baseURL}/students/${bulkStudent.id}`, authHeaders);
        }
      }
    }

    console.log('   ✅ Test data cleaned up');

    console.log('\n🎉 All API login tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Admin login: WORKING');
    console.log('   ✅ Student creation via API: WORKING');
    console.log('   ✅ Student login after creation: WORKING');
    console.log('   ✅ Student dashboard access: WORKING');
    console.log('   ✅ Teacher creation via API: WORKING');
    console.log('   ✅ Teacher login after creation: WORKING');
    console.log('   ✅ Teacher dashboard access: WORKING');
    console.log('   ✅ Bulk student creation: WORKING');
    console.log('   ✅ Bulk student login: WORKING');
    console.log('\n🚀 The API login system is fully functional!');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
    throw error;
  }
}

// Run test if called directly
if (require.main === module) {
  testAPILogin()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = testAPILogin;
