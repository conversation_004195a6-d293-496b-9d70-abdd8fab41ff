const Teacher = require('../models/Teacher');
const Class = require('../models/Class');
const User = require('../models/User');

/**
 * Fix teacher class assignments
 */
function fixTeacherAssignments() {
  console.log('🔧 FIXING TEACHER CLASS ASSIGNMENTS\n');

  try {
    // Get the math teacher
    const mathTeacher = Teacher.getAll().find(t => t.email === '<EMAIL>');
    if (!mathTeacher) {
      console.log('❌ Math teacher not found');
      return;
    }

    console.log(`📋 Math teacher: ${mathTeacher.name} (ID: ${mathTeacher.id})`);

    // Get all classes
    const allClasses = Class.findAll();
    console.log(`📚 Total classes: ${allClasses.length}`);

    // Update existing classes to be assigned to math teacher
    allClasses.forEach(cls => {
      if (cls.subject === 'Mathematics' || cls.subject === 'English') {
        console.log(`🔄 Updating class: ${cls.name} (ID: ${cls.id})`);
        console.log(`   - Current teacher ID: ${cls.teacher_id}`);
        console.log(`   - New teacher ID: ${mathTeacher.id}`);
        
        // Update the class to assign it to math teacher
        Class.update(cls.id, {
          name: cls.name,
          subject: 'Mathematics', // Change to Mathematics
          teacher_id: mathTeacher.id, // Assign to math teacher
          grade: cls.grade,
          section: cls.section,
          schedule: cls.schedule,
          virtual_link: cls.virtual_link
        });
        
        console.log(`   ✅ Updated successfully`);
      }
    });

    // Create additional classes for the math teacher if needed
    const mathClasses = Class.findByTeacher(mathTeacher.id);
    console.log(`\n📊 Math teacher now has ${mathClasses.length} classes`);

    if (mathClasses.length < 2) {
      console.log('🆕 Creating additional classes for math teacher...');
      
      // Create Mathematics Grade 10B
      const newClass = Class.create({
        name: 'Mathematics Grade 10B',
        subject: 'Mathematics',
        teacher_id: mathTeacher.id,
        grade: '10',
        section: 'B',
        schedule: 'Mon, Wed, Fri 11:00-12:00 PM',
        virtual_link: 'https://meet.google.com/math-10b'
      });
      
      console.log(`   ✅ Created: ${newClass.name} (ID: ${newClass.id})`);
    }

    // Verify the fix
    console.log('\n🔍 VERIFICATION:');
    const updatedMathClasses = Class.findByTeacher(mathTeacher.id);
    console.log(`Math teacher now has ${updatedMathClasses.length} classes:`);
    
    updatedMathClasses.forEach((cls, index) => {
      console.log(`   ${index + 1}. ${cls.name} (Grade ${cls.grade}${cls.section})`);
    });

    console.log('\n✅ Teacher assignments fixed successfully!');

  } catch (error) {
    console.error('❌ Fix failed:', error);
    throw error;
  }
}

// Run fix if called directly
if (require.main === module) {
  fixTeacherAssignments();
}

module.exports = fixTeacherAssignments;
