const User = require('../models/User');
const Student = require('../models/Student');
const Teacher = require('../models/Teacher');
const Class = require('../models/Class');
const Notification = require('../models/Notification');

const seedDatabase = async () => {
  try {
    // Check if admin already exists
    const existingAdmin = User.findByEmail('<EMAIL>');
    if (existingAdmin) {
      console.log('Admin exists, checking for additional data...');

      // Check if teachers have profiles
      const mathTeacher = User.findByEmail('<EMAIL>');
      if (mathTeacher) {
        const mathTeacherProfile = Teacher.findByUserId(mathTeacher.id);
        if (!mathTeacherProfile) {
          console.log('Adding missing teacher profiles...');

          const mathProfile = Teacher.create({
            user_id: mathTeacher.id,
            teacher_id: 'TCH001',
            subject: 'Mathematics',
            qualification: 'M.Sc Mathematics',
            experience: 5
          });

          const englishTeacher = User.findByEmail('<EMAIL>');
          if (englishTeacher) {
            Teacher.create({
              user_id: englishTeacher.id,
              teacher_id: 'TCH002',
              subject: 'English',
              qualification: 'M.A English Literature',
              experience: 3
            });
          }
          console.log('✅ Teacher profiles created');

          // Create classes
          Class.create({
            name: 'Mathematics Grade 10A',
            subject: 'Mathematics',
            teacher_id: mathProfile.id,
            grade: '10',
            section: 'A',
            schedule: 'Mon, Wed, Fri 9:00-10:00 AM',
            virtual_link: 'https://meet.google.com/math-10a'
          });

          Class.create({
            name: 'Mathematics Grade 10B',
            subject: 'Mathematics',
            teacher_id: mathProfile.id,
            grade: '10',
            section: 'B',
            schedule: 'Mon, Wed, Fri 11:00-12:00 PM',
            virtual_link: 'https://meet.google.com/math-10b'
          });
          console.log('✅ Classes created');

          // Create welcome notifications
          Notification.broadcastToRole('student', {
            title: 'Welcome to the Student Management System!',
            message: 'Your account has been set up successfully. You can now view your attendance, scores, and class schedules.',
            type: 'info',
            data: { source: 'system' }
          });

          Notification.broadcastToRole('teacher', {
            title: 'Teacher Portal Ready',
            message: 'Welcome to the teacher portal! You can now mark attendance, add scores, and manage your classes.',
            type: 'info',
            data: { source: 'system' }
          });
          console.log('✅ Welcome notifications sent');
        }
      }
      return;
    }

    console.log('Seeding database with demo data...');

    // Create admin user
    const admin = User.create({
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      name: 'System Administrator',
      phone: '******-0001'
    });
    console.log('✅ Admin user created');

    // Create teachers
    const mathTeacher = User.create({
      email: '<EMAIL>',
      password: 'teacher123',
      role: 'teacher',
      name: 'Sarah Johnson',
      phone: '******-0002'
    });

    const englishTeacher = User.create({
      email: '<EMAIL>',
      password: 'teacher123',
      role: 'teacher',
      name: 'Michael Brown',
      phone: '******-0003'
    });
    console.log('✅ Teacher users created');

    // Create teacher profiles
    const mathTeacherProfile = Teacher.create({
      user_id: mathTeacher.id,
      teacher_id: 'TCH001',
      subject: 'Mathematics',
      qualification: 'M.Sc Mathematics',
      experience: 5
    });

    const englishTeacherProfile = Teacher.create({
      user_id: englishTeacher.id,
      teacher_id: 'TCH002',
      subject: 'English',
      qualification: 'M.A English Literature',
      experience: 3
    });
    console.log('✅ Teacher profiles created');

    // Create students
    const students = [
      {
        email: '<EMAIL>',
        password: 'student123',
        name: 'John Smith',
        phone: '******-1001',
        student_id: 'STU001',
        grade: '10',
        section: 'A',
        parent_name: 'Robert Smith',
        parent_phone: '******-2001',
        address: '123 Main St, City, State 12345'
      },
      {
        email: '<EMAIL>',
        password: 'student123',
        name: 'Jane Doe',
        phone: '******-1002',
        student_id: 'STU002',
        grade: '10',
        section: 'A',
        parent_name: 'Mary Doe',
        parent_phone: '******-2002',
        address: '456 Oak Ave, City, State 12345'
      },
      {
        email: '<EMAIL>',
        password: 'student123',
        name: 'Mike Wilson',
        phone: '******-1003',
        student_id: 'STU003',
        grade: '10',
        section: 'B',
        parent_name: 'David Wilson',
        parent_phone: '******-2003',
        address: '789 Pine Rd, City, State 12345'
      },
      {
        email: '<EMAIL>',
        password: 'student123',
        name: 'Sara Davis',
        phone: '******-1004',
        student_id: 'STU004',
        grade: '10',
        section: 'B',
        parent_name: 'Lisa Davis',
        parent_phone: '******-2004',
        address: '321 Elm St, City, State 12345'
      }
    ];

    for (const studentData of students) {
      const { email, password, name, phone, ...studentProfile } = studentData;
      
      // Create user account
      const user = User.create({
        email,
        password,
        role: 'student',
        name,
        phone
      });

      // Create student profile
      Student.create({
        user_id: user.id,
        ...studentProfile
      });
    }
    console.log('✅ Student users and profiles created');

    // Create classes
    const mathClass10A = Class.create({
      name: 'Mathematics Grade 10A',
      subject: 'Mathematics',
      teacher_id: mathTeacherProfile.id,
      grade: '10',
      section: 'A',
      schedule: 'Mon, Wed, Fri 9:00-10:00 AM',
      virtual_link: 'https://meet.google.com/math-10a'
    });

    const mathClass10B = Class.create({
      name: 'Mathematics Grade 10B',
      subject: 'Mathematics',
      teacher_id: mathTeacherProfile.id,
      grade: '10',
      section: 'B',
      schedule: 'Mon, Wed, Fri 11:00-12:00 PM',
      virtual_link: 'https://meet.google.com/math-10b'
    });

    const englishClass10A = Class.create({
      name: 'English Grade 10A',
      subject: 'English',
      teacher_id: englishTeacherProfile.id,
      grade: '10',
      section: 'A',
      schedule: 'Tue, Thu 9:00-10:00 AM',
      virtual_link: 'https://meet.google.com/eng-10a'
    });

    const englishClass10B = Class.create({
      name: 'English Grade 10B',
      subject: 'English',
      teacher_id: englishTeacherProfile.id,
      grade: '10',
      section: 'B',
      schedule: 'Tue, Thu 11:00-12:00 PM',
      virtual_link: 'https://meet.google.com/eng-10b'
    });
    console.log('✅ Classes created');

    // Create welcome notifications
    Notification.broadcastToRole('student', {
      title: 'Welcome to the Student Management System!',
      message: 'Your account has been set up successfully. You can now view your attendance, scores, and class schedules.',
      type: 'info',
      data: { source: 'system' }
    });

    Notification.broadcastToRole('teacher', {
      title: 'Teacher Portal Ready',
      message: 'Welcome to the teacher portal! You can now mark attendance, add scores, and manage your classes.',
      type: 'info',
      data: { source: 'system' }
    });
    console.log('✅ Welcome notifications sent');

    console.log('\n🎉 Database seeded successfully!');
    console.log('\n📋 Demo Accounts:');
    console.log('👨‍💼 Admin: <EMAIL> / admin123');
    console.log('👩‍🏫 Teachers: <EMAIL> / teacher123');
    console.log('           <EMAIL> / teacher123');
    console.log('🎓 Students: <EMAIL> / student123');
    console.log('           <EMAIL> / student123');
    console.log('           <EMAIL> / student123');
    console.log('           <EMAIL> / student123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = seedDatabase;
