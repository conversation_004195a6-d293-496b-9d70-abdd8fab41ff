const fs = require('fs');
const path = require('path');

/**
 * Reset settings to default values
 */
function resetSettings() {
  console.log('🔄 RESETTING SETTINGS TO DEFAULT VALUES\n');

  const SETTINGS_FILE = path.join(__dirname, '../data/settings.json');
  
  const DEFAULT_SETTINGS = {
    grades: ['9', '10', '11', '12'],
    sections: ['A', 'B', 'C'],
    subjects: [
      'Mathematics',
      'English',
      'Physics',
      'Chemistry',
      'Biology',
      'History',
      'Geography',
      'Computer Science',
      'Islamic Studies',
      'Arabic'
    ],
    school: {
      name: 'HIKMAAH',
      code: 'HS001',
      academic_year: '2024-2025',
      current_semester: '1'
    },
    system: {
      email_notifications: true,
      auto_backup: true,
      maintenance_mode: false
    }
  };

  try {
    // Ensure data directory exists
    const dataDir = path.dirname(SETTINGS_FILE);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('📁 Created data directory');
    }

    // Write default settings
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(DEFAULT_SETTINGS, null, 2));
    console.log('✅ Settings file reset successfully');
    
    // Verify the settings
    const savedSettings = JSON.parse(fs.readFileSync(SETTINGS_FILE, 'utf8'));
    console.log('\n📋 Verified Settings:');
    console.log(`   Grades: ${savedSettings.grades.join(', ')}`);
    console.log(`   Sections: ${savedSettings.sections.join(', ')}`);
    console.log(`   Subjects: ${savedSettings.subjects.slice(0, 3).join(', ')}... (${savedSettings.subjects.length} total)`);
    console.log(`   School: ${savedSettings.school.name}`);
    
    console.log('\n🎉 Settings reset completed successfully!');

  } catch (error) {
    console.error('❌ Reset failed:', error);
    throw error;
  }
}

// Run reset if called directly
if (require.main === module) {
  resetSettings();
}

module.exports = resetSettings;
