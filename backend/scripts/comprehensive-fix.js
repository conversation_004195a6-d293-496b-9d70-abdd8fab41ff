const axios = require('axios');

/**
 * Comprehensive fix and test script
 */
async function comprehensiveFix() {
  console.log('🔧 COMPREHENSIVE FIX AND TEST\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Test 1: Teacher Login and Verify Profile Data
    console.log('👩‍🏫 Test 1: Teacher Authentication...');
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });

    console.log('✅ Teacher login successful');
    console.log(`   User: ${teacherLoginResponse.data.user.name}`);
    console.log(`   Teacher ID: ${teacherLoginResponse.data.user.teacher?.id}`);
    console.log(`   Subject: ${teacherLoginResponse.data.user.teacher?.subject}`);

    const teacherToken = teacherLoginResponse.data.token;
    const teacherId = teacherLoginResponse.data.user.teacher.id;
    const teacherAuthHeaders = { headers: { Authorization: `Bearer ${teacherToken}` } };

    // Test 2: Verify Teacher Classes
    console.log('\n📚 Test 2: Teacher Classes...');
    const classesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherId}`, teacherAuthHeaders);
    const classes = classesResponse.data.classes;
    
    console.log(`✅ Found ${classes.length} classes for teacher`);
    classes.forEach((cls, index) => {
      console.log(`   ${index + 1}. ${cls.name} (Grade ${cls.grade}${cls.section})`);
    });

    // Test 3: Test Attendance Workflow
    console.log('\n📋 Test 3: Attendance Workflow...');
    if (classes.length > 0) {
      const testClass = classes[0];
      console.log(`   Using class: ${testClass.name}`);
      
      // Get students for this class
      const studentsResponse = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherAuthHeaders);
      const students = studentsResponse.data.students;
      console.log(`   Found ${students.length} students`);
      
      if (students.length > 0) {
        // Test attendance marking
        const attendanceData = {
          student_id: students[0].id,
          class_id: testClass.id,
          date: '2025-06-24',
          status: 'present',
          notes: 'Comprehensive test'
        };
        
        const markResponse = await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherAuthHeaders);
        console.log('   ✅ Attendance marking successful');
        
        // Test attendance retrieval
        const attendanceResponse = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=2025-06-24`, teacherAuthHeaders);
        console.log(`   ✅ Attendance retrieval successful (${attendanceResponse.data.attendance.length} records)`);
      }
    }

    // Test 4: Admin Class Management
    console.log('\n👨‍💼 Test 4: Admin Class Management...');
    const adminLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLoginResponse.data.token;
    const adminAuthHeaders = { headers: { Authorization: `Bearer ${adminToken}` } };

    // Test class editing
    const allClassesResponse = await axios.get(`${baseURL}/classes`, adminAuthHeaders);
    const testClass = allClassesResponse.data.classes[0];
    
    const updateData = {
      name: testClass.name,
      subject: testClass.subject,
      teacher_id: testClass.teacher_id,
      grade: testClass.grade,
      section: testClass.section,
      schedule: testClass.schedule || 'Updated schedule',
      room: 'Room 101'
    };
    
    const updateResponse = await axios.put(`${baseURL}/classes/${testClass.id}`, updateData, adminAuthHeaders);
    console.log('   ✅ Class editing successful');

    // Test 5: Settings Management
    console.log('\n⚙️ Test 5: Settings Management...');
    
    // Get current settings
    const settingsResponse = await axios.get(`${baseURL}/settings`, adminAuthHeaders);
    const currentSettings = settingsResponse.data.data;
    console.log(`   Current grades: ${currentSettings.grades.join(', ')}`);
    console.log(`   Current sections: ${currentSettings.sections.join(', ')}`);
    
    // Test grade update
    const testGrades = [...currentSettings.grades, 'TestGrade'];
    await axios.put(`${baseURL}/settings/grades`, { grades: testGrades }, adminAuthHeaders);
    
    // Verify update
    const updatedSettingsResponse = await axios.get(`${baseURL}/settings`, adminAuthHeaders);
    const updatedGrades = updatedSettingsResponse.data.data.grades;
    
    if (updatedGrades.includes('TestGrade')) {
      console.log('   ✅ Settings update successful');
      
      // Restore original settings
      await axios.put(`${baseURL}/settings/grades`, { grades: currentSettings.grades }, adminAuthHeaders);
      console.log('   ✅ Settings restored');
    }

    // Test 6: Student Access
    console.log('\n🎓 Test 6: Student Access...');
    const studentLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'student123'
    });

    const studentToken = studentLoginResponse.data.token;
    const studentId = studentLoginResponse.data.user.student.id;
    const studentAuthHeaders = { headers: { Authorization: `Bearer ${studentToken}` } };

    // Test student data access
    const studentAttendanceResponse = await axios.get(`${baseURL}/attendance/student/${studentId}`, studentAuthHeaders);
    const studentClassesResponse = await axios.get(`${baseURL}/classes/student/${studentId}`, studentAuthHeaders);
    const studentScoresResponse = await axios.get(`${baseURL}/scores/student/${studentId}`, studentAuthHeaders);
    
    console.log(`   ✅ Student attendance: ${studentAttendanceResponse.data.attendance.length} records`);
    console.log(`   ✅ Student classes: ${studentClassesResponse.data.classes.length} classes`);
    console.log(`   ✅ Student scores: ${studentScoresResponse.data.scores.length} scores`);

    console.log('\n🎉 ALL TESTS PASSED SUCCESSFULLY!');
    console.log('\n📋 System Status:');
    console.log('   ✅ Teacher authentication with profile data');
    console.log('   ✅ Teacher class assignment and retrieval');
    console.log('   ✅ Attendance marking and retrieval');
    console.log('   ✅ Class editing and management');
    console.log('   ✅ Settings management and persistence');
    console.log('   ✅ Student data access and security');
    console.log('\n🚀 Backend is fully functional!');
    console.log('\n💡 If frontend issues persist, try:');
    console.log('   1. Clear browser cache and reload');
    console.log('   2. Check browser console for JavaScript errors');
    console.log('   3. Verify frontend is connecting to http://localhost:3001');
    console.log('   4. Restart the frontend development server');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

// Run test if called directly
if (require.main === module) {
  comprehensiveFix()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = comprehensiveFix;
