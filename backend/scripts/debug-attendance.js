const axios = require('axios');

async function debugAttendance() {
  console.log('🔍 DEBUGGING ATTENDANCE ISSUE\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Login as teacher
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });
    
    const teacherToken = teacherLoginResponse.data.token;
    const teacherUser = teacherLoginResponse.data.user;
    const teacherAuthHeaders = {
      headers: { 'Authorization': `Bearer ${teacherToken}` }
    };
    
    console.log(`Teacher: ${teacherUser.name} (ID: ${teacherUser.teacher.id})`);

    // Get classes
    const classesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherUser.teacher.id}`, teacherAuthHeaders);
    const classes = classesResponse.data.classes;
    const testClass = classes[0];
    
    console.log(`\nClass: ${testClass.name}`);
    console.log(`Class ID: ${testClass.id}`);
    console.log(`Grade: ${testClass.grade}, Section: ${testClass.section}`);

    // Get students
    const studentsResponse = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherAuthHeaders);
    const students = studentsResponse.data.students;
    const testStudent = students[0];
    
    console.log(`\nStudent: ${testStudent.name}`);
    console.log(`Student ID: ${testStudent.id}`);
    console.log(`Student Grade: ${testStudent.grade}, Section: ${testStudent.section}`);

    // Mark attendance
    const testDate = '2025-06-24';
    const attendanceData = {
      student_id: testStudent.id,
      class_id: testClass.id,
      date: testDate,
      status: 'present',
      notes: 'Debug test'
    };

    console.log(`\nMarking attendance with data:`, attendanceData);
    const markResponse = await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherAuthHeaders);
    console.log('Mark response:', markResponse.data);

    // Get all attendance records for this class and date
    console.log(`\nGetting attendance records for class ${testClass.id} on ${testDate}...`);
    const attendanceResponse = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=${testDate}`, teacherAuthHeaders);
    const attendanceRecords = attendanceResponse.data.attendance;
    
    console.log(`Found ${attendanceRecords.length} attendance records:`);
    attendanceRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. Student ID: ${record.student_id}, Name: ${record.student_name}, Status: ${record.status}`);
    });

    // Check if our specific student is in the records
    const ourRecord = attendanceRecords.find(r => r.student_id === testStudent.id);
    if (ourRecord) {
      console.log(`\n✅ Found our student's record:`, ourRecord);
    } else {
      console.log(`\n❌ Our student's record not found`);
      console.log(`Looking for student_id: ${testStudent.id}`);
      console.log(`Available student_ids: ${attendanceRecords.map(r => r.student_id).join(', ')}`);
    }

    // Also check by getting attendance for the specific student
    console.log(`\nGetting attendance records for student ${testStudent.id}...`);
    const studentAttendanceResponse = await axios.get(`${baseURL}/attendance/student/${testStudent.id}`, teacherAuthHeaders);
    const studentRecords = studentAttendanceResponse.data.attendance;
    console.log(`Student has ${studentRecords.length} attendance records total:`);
    studentRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. Date: ${record.date}, Class: ${record.class_name}, Status: ${record.status}`);
    });

  } catch (error) {
    console.error('❌ DEBUG FAILED:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

debugAttendance();
