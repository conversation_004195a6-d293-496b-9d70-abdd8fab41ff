const Teacher = require('../models/Teacher');
const Class = require('../models/Class');
const User = require('../models/User');

/**
 * Debug teacher class assignments
 */
function debugTeacherClasses() {
  console.log('🔍 DEBUGGING TEACHER CLASS ASSIGNMENTS\n');

  try {
    // Get all teachers
    const teachers = Teacher.getAll();
    console.log(`📊 Total teachers: ${teachers.length}`);
    
    teachers.forEach((teacher, index) => {
      console.log(`\n${index + 1}. Teacher: ${teacher.name}`);
      console.log(`   - ID: ${teacher.id}`);
      console.log(`   - User ID: ${teacher.user_id}`);
      console.log(`   - Subject: ${teacher.subject}`);
      
      // Get classes for this teacher
      const classes = Class.findByTeacher(teacher.id);
      console.log(`   - Classes assigned: ${classes.length}`);
      
      if (classes.length > 0) {
        classes.forEach((cls, clsIndex) => {
          console.log(`     ${clsIndex + 1}. ${cls.name} (Grade ${cls.grade}${cls.section})`);
        });
      }
    });

    // Get all classes and their teacher assignments
    console.log('\n📚 ALL CLASSES:');
    const allClasses = Class.findAll();
    console.log(`Total classes: ${allClasses.length}`);
    
    allClasses.forEach((cls, index) => {
      console.log(`\n${index + 1}. ${cls.name}`);
      console.log(`   - ID: ${cls.id}`);
      console.log(`   - Teacher ID: ${cls.teacher_id}`);
      console.log(`   - Teacher Name: ${cls.teacher_name || 'Not assigned'}`);
      console.log(`   - Subject: ${cls.subject}`);
      console.log(`   - Grade/Section: ${cls.grade}${cls.section}`);
    });

    // Check for math teacher specifically
    console.log('\n🔍 MATH TEACHER SPECIFIC CHECK:');
    const mathTeacher = teachers.find(t => t.email === '<EMAIL>');
    if (mathTeacher) {
      console.log(`Math teacher found: ${mathTeacher.name} (ID: ${mathTeacher.id})`);
      const mathClasses = Class.findByTeacher(mathTeacher.id);
      console.log(`Math teacher classes: ${mathClasses.length}`);
      
      // Check if there are classes with math teacher's ID
      const classesWithMathTeacher = allClasses.filter(c => c.teacher_id === mathTeacher.id);
      console.log(`Classes with math teacher ID: ${classesWithMathTeacher.length}`);
      
      if (classesWithMathTeacher.length > 0) {
        classesWithMathTeacher.forEach(cls => {
          console.log(`   - ${cls.name} (Teacher ID: ${cls.teacher_id})`);
        });
      }
    } else {
      console.log('Math teacher not found!');
    }

    // Check for orphaned classes (no teacher assigned)
    console.log('\n🚨 ORPHANED CLASSES (no teacher):');
    const orphanedClasses = allClasses.filter(c => !c.teacher_id || c.teacher_id === null);
    console.log(`Orphaned classes: ${orphanedClasses.length}`);
    
    orphanedClasses.forEach(cls => {
      console.log(`   - ${cls.name} (ID: ${cls.id})`);
    });

    console.log('\n🎯 SUMMARY:');
    console.log(`   - Total teachers: ${teachers.length}`);
    console.log(`   - Total classes: ${allClasses.length}`);
    console.log(`   - Orphaned classes: ${orphanedClasses.length}`);
    console.log(`   - Classes with teachers: ${allClasses.length - orphanedClasses.length}`);

  } catch (error) {
    console.error('❌ Debug failed:', error);
    throw error;
  }
}

// Run debug if called directly
if (require.main === module) {
  debugTeacherClasses();
}

module.exports = debugTeacherClasses;
