const axios = require('axios');
const Teacher = require('../models/Teacher');
const Class = require('../models/Class');
const User = require('../models/User');

/**
 * Debug script to identify and fix current issues
 */
async function debugIssues() {
  console.log('🔍 DEBUGGING CURRENT ISSUES\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Debug 1: Check teacher-class assignments in database
    console.log('📊 Debug 1: Database State Check...');
    
    const allTeachers = Teacher.getAll();
    const allClasses = Class.findAll();
    
    console.log(`   Teachers in DB: ${allTeachers.length}`);
    allTeachers.forEach(teacher => {
      console.log(`   - ${teacher.name} (ID: ${teacher.id}, User ID: ${teacher.user_id})`);
    });
    
    console.log(`   Classes in DB: ${allClasses.length}`);
    allClasses.forEach(cls => {
      console.log(`   - ${cls.name} (Teacher ID: ${cls.teacher_id})`);
    });

    // Debug 2: Test teacher login and class retrieval
    console.log('\n👩‍🏫 Debug 2: Teacher Login and Class Access...');
    const teacherLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });

    console.log('   Teacher login response:');
    console.log(`   - User ID: ${teacherLoginResponse.data.user.id}`);
    console.log(`   - Teacher Profile: ${JSON.stringify(teacherLoginResponse.data.user.teacher)}`);

    const teacherToken = teacherLoginResponse.data.token;
    const teacherId = teacherLoginResponse.data.user.teacher?.id;
    const teacherAuthHeaders = { headers: { Authorization: `Bearer ${teacherToken}` } };

    if (teacherId) {
      const teacherClassesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherId}`, teacherAuthHeaders);
      console.log(`   - Classes assigned: ${teacherClassesResponse.data.classes.length}`);
      teacherClassesResponse.data.classes.forEach(cls => {
        console.log(`     * ${cls.name} (ID: ${cls.id})`);
      });
    } else {
      console.log('   ❌ No teacher ID found in login response');
    }

    // Debug 3: Test attendance marking with detailed error info
    console.log('\n📋 Debug 3: Attendance Marking Test...');
    
    if (teacherId) {
      const classesResponse = await axios.get(`${baseURL}/classes/teacher/${teacherId}`, teacherAuthHeaders);
      const classes = classesResponse.data.classes;
      
      if (classes.length > 0) {
        const testClass = classes[0];
        console.log(`   Testing with class: ${testClass.name} (ID: ${testClass.id})`);
        
        // Get students for this class
        const studentsResponse = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherAuthHeaders);
        const students = studentsResponse.data.students;
        console.log(`   Students found: ${students.length}`);
        
        if (students.length > 0) {
          const testStudent = students[0];
          const attendanceData = {
            student_id: testStudent.id,
            class_id: testClass.id,
            date: '2025-06-24',
            status: 'present',
            notes: 'Debug test'
          };
          
          console.log('   Attendance data to send:', JSON.stringify(attendanceData, null, 2));
          
          try {
            const markResponse = await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherAuthHeaders);
            console.log('   ✅ Attendance marking successful');
          } catch (error) {
            console.log('   ❌ Attendance marking failed:');
            console.log(`   Status: ${error.response?.status}`);
            console.log(`   Error: ${JSON.stringify(error.response?.data, null, 2)}`);
          }
        }
      }
    }

    // Debug 4: Test class editing with detailed error info
    console.log('\n📝 Debug 4: Class Editing Test...');
    
    const adminLoginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = adminLoginResponse.data.token;
    const adminAuthHeaders = { headers: { Authorization: `Bearer ${adminToken}` } };

    const allClassesResponse = await axios.get(`${baseURL}/classes`, adminAuthHeaders);
    const testClass = allClassesResponse.data.classes[0];
    
    console.log(`   Testing with class: ${testClass.name} (ID: ${testClass.id})`);
    
    const updateData = {
      name: testClass.name,
      subject: testClass.subject,
      teacher_id: testClass.teacher_id,
      grade: testClass.grade,
      section: testClass.section,
      schedule: testClass.schedule || '',
      room: testClass.room || ''
    };
    
    console.log('   Update data to send:', JSON.stringify(updateData, null, 2));
    
    try {
      const updateResponse = await axios.put(`${baseURL}/classes/${testClass.id}`, updateData, adminAuthHeaders);
      console.log('   ✅ Class editing successful');
    } catch (error) {
      console.log('   ❌ Class editing failed:');
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Error: ${JSON.stringify(error.response?.data, null, 2)}`);
    }

    // Debug 5: Check CORS headers
    console.log('\n🌐 Debug 5: CORS Test...');
    try {
      const corsResponse = await axios.options(`${baseURL}/classes`, {
        headers: {
          'Origin': 'http://localhost:5173',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'authorization'
        }
      });
      console.log('   ✅ CORS preflight successful');
      console.log(`   CORS headers: ${JSON.stringify(corsResponse.headers, null, 2)}`);
    } catch (error) {
      console.log('   ❌ CORS preflight failed:');
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Error: ${error.message}`);
    }

    console.log('\n🎯 Debug Summary Complete');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
    throw error;
  }
}

// Run debug if called directly
if (require.main === module) {
  debugIssues()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = debugIssues;
