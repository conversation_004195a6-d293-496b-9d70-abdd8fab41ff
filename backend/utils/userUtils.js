const User = require('../models/User');
const Student = require('../models/Student');
const Teacher = require('../models/Teacher');

/**
 * Utility functions for user management
 */

/**
 * Validate email format
 * @param {string} email 
 * @returns {boolean}
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generate a secure random password
 * @param {number} length 
 * @returns {string}
 */
function generateSecurePassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

/**
 * Create multiple students from CSV-like data
 * @param {Array} studentsData - Array of student objects
 * @returns {Object} - Results with success/failure counts
 */
async function bulkCreateStudents(studentsData) {
  const results = {
    success: [],
    failed: [],
    total: studentsData.length
  };

  for (const studentData of studentsData) {
    try {
      // Validate required fields
      if (!studentData.email || !studentData.name || !studentData.student_id) {
        results.failed.push({
          data: studentData,
          error: 'Missing required fields: email, name, or student_id'
        });
        continue;
      }

      // Validate email format
      if (!isValidEmail(studentData.email)) {
        results.failed.push({
          data: studentData,
          error: 'Invalid email format'
        });
        continue;
      }

      // Check for existing email
      const existingUser = User.findByEmail(studentData.email);
      if (existingUser) {
        results.failed.push({
          data: studentData,
          error: 'Email already exists'
        });
        continue;
      }

      // Check for existing student ID
      const existingStudent = Student.findByStudentId(studentData.student_id);
      if (existingStudent) {
        results.failed.push({
          data: studentData,
          error: 'Student ID already exists'
        });
        continue;
      }

      // Generate password if not provided
      const password = studentData.password || generateSecurePassword(8);

      // Create user account
      const user = User.create({
        email: studentData.email,
        password: password,
        role: 'student',
        name: studentData.name,
        phone: studentData.phone || null
      });

      // Create student profile
      const student = Student.create({
        user_id: user.id,
        student_id: studentData.student_id,
        grade: studentData.grade || '10',
        section: studentData.section || 'A',
        parent_name: studentData.parent_name || null,
        parent_phone: studentData.parent_phone || null,
        address: studentData.address || null
      });

      results.success.push({
        student: student,
        email: studentData.email,
        password: password,
        generated_password: !studentData.password
      });

    } catch (error) {
      results.failed.push({
        data: studentData,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Create multiple teachers from CSV-like data
 * @param {Array} teachersData - Array of teacher objects
 * @returns {Object} - Results with success/failure counts
 */
async function bulkCreateTeachers(teachersData) {
  const results = {
    success: [],
    failed: [],
    total: teachersData.length
  };

  for (const teacherData of teachersData) {
    try {
      // Validate required fields
      if (!teacherData.email || !teacherData.name || !teacherData.teacher_id || !teacherData.subject) {
        results.failed.push({
          data: teacherData,
          error: 'Missing required fields: email, name, teacher_id, or subject'
        });
        continue;
      }

      // Validate email format
      if (!isValidEmail(teacherData.email)) {
        results.failed.push({
          data: teacherData,
          error: 'Invalid email format'
        });
        continue;
      }

      // Check for existing email
      const existingUser = User.findByEmail(teacherData.email);
      if (existingUser) {
        results.failed.push({
          data: teacherData,
          error: 'Email already exists'
        });
        continue;
      }

      // Check for existing teacher ID
      const existingTeacher = Teacher.findByTeacherId(teacherData.teacher_id);
      if (existingTeacher) {
        results.failed.push({
          data: teacherData,
          error: 'Teacher ID already exists'
        });
        continue;
      }

      // Generate password if not provided
      const password = teacherData.password || generateSecurePassword(8);

      // Create user account
      const user = User.create({
        email: teacherData.email,
        password: password,
        role: 'teacher',
        name: teacherData.name,
        phone: teacherData.phone || null
      });

      // Create teacher profile
      const teacher = Teacher.create({
        user_id: user.id,
        teacher_id: teacherData.teacher_id,
        subject: teacherData.subject,
        qualification: teacherData.qualification || null,
        experience: teacherData.experience || 0
      });

      results.success.push({
        teacher: teacher,
        email: teacherData.email,
        password: password,
        generated_password: !teacherData.password
      });

    } catch (error) {
      results.failed.push({
        data: teacherData,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Get user statistics
 * @returns {Object} - User statistics
 */
function getUserStatistics() {
  const userStats = User.getStats();
  const studentStats = Student.getGradeStats();
  
  return {
    users: userStats,
    students: {
      byGrade: studentStats,
      total: studentStats.reduce((sum, stat) => sum + stat.student_count, 0)
    },
    teachers: {
      total: userStats.find(stat => stat.role === 'teacher')?.count || 0
    },
    admins: {
      total: userStats.find(stat => stat.role === 'admin')?.count || 0
    }
  };
}

/**
 * Validate user data before creation
 * @param {Object} userData 
 * @param {string} userType - 'student' or 'teacher'
 * @returns {Object} - Validation result
 */
function validateUserData(userData, userType) {
  const errors = [];

  // Common validations
  if (!userData.email) {
    errors.push('Email is required');
  } else if (!isValidEmail(userData.email)) {
    errors.push('Invalid email format');
  }

  if (!userData.name || userData.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  }

  if (userData.password && userData.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  // Student-specific validations
  if (userType === 'student') {
    if (!userData.student_id) {
      errors.push('Student ID is required');
    }
    if (!userData.grade) {
      errors.push('Grade is required');
    }
    if (!userData.section) {
      errors.push('Section is required');
    }
  }

  // Teacher-specific validations
  if (userType === 'teacher') {
    if (!userData.teacher_id) {
      errors.push('Teacher ID is required');
    }
    if (!userData.subject) {
      errors.push('Subject is required');
    }
    if (userData.experience && (isNaN(userData.experience) || userData.experience < 0)) {
      errors.push('Experience must be a non-negative number');
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

module.exports = {
  isValidEmail,
  generateSecurePassword,
  bulkCreateStudents,
  bulkCreateTeachers,
  getUserStatistics,
  validateUserData
};
