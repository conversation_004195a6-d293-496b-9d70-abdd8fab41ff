# Student Management System

A complete full-stack student management system built with React and Node.js.

## Tech Stack
- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: Node.js + Express.js
- **Database**: SQLite with better-sqlite3
- **Authentication**: JWT tokens

## Quick Start

1. **Install all dependencies**:
   ```bash
   npm run install-all
   ```

2. **Start development servers**:
   ```bash
   npm run dev
   ```

3. **Access the application**:
   - Frontend: http://localhost:5175
   - Backend API: http://localhost:5001

## Demo Accounts

### Admin
- Email: <EMAIL>
- Password: admin123

### Teachers
- Email: <EMAIL> | Password: teacher123
- Email: <EMAIL> | Password: teacher123

### Students
- Email: <EMAIL> | Password: student123
- Email: <EMAIL> | Password: student123
- Email: <EMAIL> | Password: student123
- Email: <EMAIL> | Password: student123

## Features

### Admin Dashboard
- Manage students, teachers, and classes
- Generate reports and analytics
- System administration

### Teacher Portal
- Mark attendance with calendar view
- Manage assigned students
- Add activities and scores
- Create virtual class links

### Student Portal (Mobile-First)
- View personal dashboard
- Check attendance history
- View grades and scores
- Access class schedules

## Project Structure
```
student-management/
├── frontend/          # React app (port 5175)
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Page components (Admin, Teacher, Student)
│   │   ├── contexts/      # React contexts (Auth)
│   │   ├── hooks/         # Custom React hooks
│   │   ├── utils/         # API utilities
│   │   └── services/      # Business logic
│   ├── public/
│   └── package.json
├── backend/           # Node.js API (port 5001)
│   ├── controllers/       # Route handlers
│   ├── middleware/        # Auth, validation middleware
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── scripts/          # Database seeding
│   ├── utils/            # Helper utilities
│   ├── database/         # SQLite database
│   └── package.json
├── package.json       # Root package.json for scripts
└── README.md
```

## Getting Started Commands
```bash
# Install all dependencies
npm run install-all

# Start both frontend and backend
npm run dev

# Or start individually:
npm run frontend  # Frontend: http://localhost:5175
npm run backend   # Backend: http://localhost:5001
```

## ✅ Implementation Status

### ✅ Completed Features

#### 🔐 Authentication & Security
- **JWT Authentication**: Role-based access control (Admin, Teacher, Student)
- **Password Security**: Bcrypt hashing with salt
- **Input Validation**: Joi schema validation for all endpoints
- **CORS Protection**: Configurable cross-origin resource sharing
- **Rate Limiting**: Protection against abuse and DDoS

#### 👨‍💼 Admin Dashboard
- **User Management**: Create, edit, delete students and teachers
- **System Statistics**: Real-time overview of users, classes, attendance
- **Student Records**: Complete CRUD operations with search and filtering
- **Report Generation**: Attendance, score, and overview reports with CSV export
- **Notification Broadcasting**: Send announcements to specific roles or classes

#### 👩‍🏫 Teacher Portal
- **Class Management**: View assigned classes with student counts
- **Attendance Marking**: Interactive calendar-based attendance system
- **Score Management**: Add, edit, delete test scores and grades
- **Student Analytics**: View individual student performance and attendance
- **Real-time Dashboard**: Quick stats and recent activity overview

#### 🎓 Student Portal
- **Personal Dashboard**: Attendance percentage, recent scores, class schedule
- **Attendance History**: Detailed view of attendance records with status
- **Score Tracking**: Test results with percentage calculations and trends
- **Class Access**: Virtual class links and schedule information
- **Notifications**: Real-time updates on scores, attendance, and announcements

#### 📊 Advanced Features
- **Real-time Notifications**: Push notifications with read/unread status
- **Report Generation**: Comprehensive reports with CSV export capability
- **File Upload System**: Profile pictures, documents, and report uploads
- **Class Scheduling**: Virtual class links and timetable management
- **Analytics Dashboard**: Performance metrics and attendance analytics
- **Mobile-First Design**: Responsive interface optimized for all devices

#### 🗄️ Database & API
- **SQLite Database**: 8 interconnected tables with foreign key constraints
- **RESTful API**: 40+ endpoints with proper HTTP status codes
- **Data Relationships**: Users, Students, Teachers, Classes, Attendance, Scores, Notifications
- **Auto-seeding**: Demo data with 4 students, 2 teachers, 1 admin, and sample classes
- **Query Optimization**: Efficient joins and indexed searches

### 🔧 Technical Features
- **Hot Reload**: Both frontend and backend support live reloading
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Type Safety**: Input validation with Joi
- **Database Migrations**: Automatic table creation and seeding
- **API Documentation**: RESTful API with consistent response format
- **File Management**: Secure file upload with type validation and size limits
- **Real-time Updates**: Notification system with 30-second polling
- **CSV Export**: Generate downloadable reports in CSV format
- **Virtual Classes**: Integration-ready for video conferencing platforms

## Development

The system automatically seeds the database with demo data on first run.
Both frontend and backend support hot reload for development.

## Security Features
- JWT token authentication
- Password hashing with bcrypt
- Input validation and sanitization
- SQL injection prevention
- CORS configuration

## 📚 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/logout` - User logout
- `POST /api/auth/change-password` - Change password

### Students
- `GET /api/students` - Get all students (with filters)
- `GET /api/students/:id` - Get student by ID
- `POST /api/students` - Create new student (admin only)
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student (admin only)
- `GET /api/students/stats` - Get student statistics

### Classes
- `GET /api/classes` - Get all classes
- `GET /api/classes/:id` - Get class by ID
- `GET /api/classes/teacher/:teacherId` - Get classes by teacher
- `GET /api/classes/student/:studentId` - Get classes by student
- `POST /api/classes` - Create new class (admin only)
- `PUT /api/classes/:id` - Update class (admin only)
- `DELETE /api/classes/:id` - Delete class (admin only)

### Attendance
- `POST /api/attendance/mark` - Mark attendance (teachers/admin)
- `GET /api/attendance/student/:studentId` - Get student attendance
- `GET /api/attendance/class/:classId` - Get class attendance
- `GET /api/attendance/report` - Generate attendance report
- `DELETE /api/attendance/:id` - Delete attendance record

### Scores
- `GET /api/scores/student/:studentId` - Get student scores
- `GET /api/scores/teacher/:teacherId` - Get teacher's scores
- `GET /api/scores/analytics` - Get score analytics
- `POST /api/scores` - Add new score (teachers/admin)
- `PUT /api/scores/:id` - Update score (teachers/admin)
- `DELETE /api/scores/:id` - Delete score (teachers/admin)

### Notifications
- `GET /api/notifications` - Get user notifications
- `PATCH /api/notifications/:id/read` - Mark as read
- `PATCH /api/notifications/read-all` - Mark all as read
- `DELETE /api/notifications/:id` - Delete notification
- `POST /api/notifications` - Create notification (teachers/admin)
- `POST /api/notifications/broadcast` - Broadcast notification (admin)

### Reports
- `GET /api/reports/attendance` - Attendance report (CSV/JSON)
- `GET /api/reports/scores` - Score report (CSV/JSON)
- `GET /api/reports/student/:studentId` - Individual student report
- `GET /api/reports/overview` - System overview report (admin)

## 🚀 Production Ready
The application is production-ready and can be deployed to any hosting provider with Node.js support. The SQLite database makes it easy to deploy without external database dependencies.

### 🌟 Key Achievements
- **40+ API Endpoints**: Complete REST API coverage
- **3 Role-Based Dashboards**: Admin, Teacher, Student interfaces
- **Real-time Features**: Notifications and live updates
- **Mobile-First Design**: Responsive across all devices
- **Comprehensive Reports**: Data export and analytics
- **Security Best Practices**: Authentication, validation, rate limiting
