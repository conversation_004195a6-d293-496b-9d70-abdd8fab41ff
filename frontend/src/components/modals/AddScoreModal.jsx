import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { scoresAPI, studentsAPI, classesAPI } from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function AddScoreModal({ isOpen, onClose, score, classId, onSuccess }) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: score ? {
      student_id: score.student_id,
      subject: score.subject,
      score: score.score,
      max_score: score.max_score,
      test_date: score.test_date,
      type: score.type,
      description: score.description
    } : {
      test_date: new Date().toISOString().split('T')[0]
    }
  });

  // Get class info to get students
  const { data: classData } = useQuery({
    queryKey: ['class', classId],
    queryFn: () => classesAPI.getById(classId),
    enabled: !!classId,
  });

  // Get students for the selected class
  const { data: studentsData } = useQuery({
    queryKey: ['class-students', classId],
    queryFn: () => {
      if (classData?.data?.class) {
        const classInfo = classData.data.class;
        return studentsAPI.getAll({ grade: classInfo.grade, section: classInfo.section });
      }
      return { data: { students: [] } };
    },
    enabled: !!classData?.data?.class,
  });

  const createScoreMutation = useMutation({
    mutationFn: score ? 
      (data) => scoresAPI.update(score.id, data) :
      scoresAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['teacher-scores']);
      toast.success(score ? 'Score updated successfully' : 'Score added successfully');
      reset();
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || `Failed to ${score ? 'update' : 'add'} score`);
    },
  });

  const onSubmit = (data) => {
    createScoreMutation.mutate(data);
  };

  if (!isOpen) return null;

  const students = studentsData?.data?.students || [];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {score ? 'Edit Score' : 'Add New Score'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="label">Student *</label>
              <select
                {...register('student_id', { required: 'Student is required' })}
                className="input"
                disabled={!!score}
              >
                <option value="">Select Student</option>
                {students.map((student) => (
                  <option key={student.id} value={student.id}>
                    {student.name} - {student.student_id}
                  </option>
                ))}
              </select>
              {errors.student_id && (
                <p className="mt-1 text-sm text-red-600">{errors.student_id.message}</p>
              )}
            </div>

            <div>
              <label className="label">Subject *</label>
              <select
                {...register('subject', { required: 'Subject is required' })}
                className="input"
              >
                <option value="">Select Subject</option>
                <option value="Mathematics">Mathematics</option>
                <option value="English">English</option>
                <option value="Physics">Physics</option>
                <option value="Chemistry">Chemistry</option>
                <option value="Biology">Biology</option>
                <option value="History">History</option>
                <option value="Geography">Geography</option>
                <option value="Computer Science">Computer Science</option>
              </select>
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Score *</label>
                <input
                  {...register('score', { 
                    required: 'Score is required',
                    min: { value: 0, message: 'Score cannot be negative' }
                  })}
                  type="number"
                  step="0.1"
                  className="input"
                  placeholder="85"
                />
                {errors.score && (
                  <p className="mt-1 text-sm text-red-600">{errors.score.message}</p>
                )}
              </div>

              <div>
                <label className="label">Max Score *</label>
                <input
                  {...register('max_score', { 
                    required: 'Max score is required',
                    min: { value: 1, message: 'Max score must be at least 1' }
                  })}
                  type="number"
                  step="0.1"
                  className="input"
                  placeholder="100"
                />
                {errors.max_score && (
                  <p className="mt-1 text-sm text-red-600">{errors.max_score.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">Test Type *</label>
              <select
                {...register('type', { required: 'Test type is required' })}
                className="input"
              >
                <option value="">Select Type</option>
                <option value="quiz">Quiz</option>
                <option value="exam">Exam</option>
                <option value="assignment">Assignment</option>
                <option value="project">Project</option>
                <option value="homework">Homework</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            <div>
              <label className="label">Test Date *</label>
              <input
                {...register('test_date', { required: 'Test date is required' })}
                type="date"
                className="input"
              />
              {errors.test_date && (
                <p className="mt-1 text-sm text-red-600">{errors.test_date.message}</p>
              )}
            </div>

            <div>
              <label className="label">Description</label>
              <textarea
                {...register('description')}
                className="input"
                rows="3"
                placeholder="Optional description or notes about the test"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createScoreMutation.isLoading}
                className="btn-primary"
              >
                {createScoreMutation.isLoading ? 
                  (score ? 'Updating...' : 'Adding...') : 
                  (score ? 'Update Score' : 'Add Score')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
