import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { classesAPI, teachersAPI, settingsAPI } from '../../utils/api';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function AddClassModal({ isOpen, onClose, onSuccess }) {
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  // Get teachers for the dropdown
  const { data: teachersData } = useQuery({
    queryKey: ['teachers'],
    queryFn: () => teachersAPI.getAll(),
  });

  // Get settings for grades and sections
  const { data: settingsData } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsAPI.getSettings(),
  });

  const createClassMutation = useMutation({
    mutationFn: classesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['classes']);
      toast.success('Class created successfully');
      reset();
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to create class');
    },
  });

  const onSubmit = (data) => {
    createClassMutation.mutate(data);
  };

  if (!isOpen) return null;

  const teachers = teachersData?.data?.data?.teachers || teachersData?.data?.teachers || [];
  const grades = settingsData?.data?.grades || ['9', '10', '11', '12'];
  const sections = settingsData?.data?.sections || ['A', 'B', 'C'];
  const subjects = settingsData?.data?.subjects || ['Mathematics', 'English', 'Physics', 'Chemistry'];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add New Class</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="label">Class Name *</label>
              <input
                {...register('name', { required: 'Class name is required' })}
                className="input"
                placeholder="e.g., Mathematics Grade 10A"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="label">Subject *</label>
              <select
                {...register('subject', { required: 'Subject is required' })}
                className="input"
              >
                <option value="">Select Subject</option>
                {subjects.map(subject => (
                  <option key={subject} value={subject}>{subject}</option>
                ))}
              </select>
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            <div>
              <label className="label">Teacher</label>
              <select
                {...register('teacher_id')}
                className="input"
              >
                <option value="">Select Teacher</option>
                {teachers.map((teacher) => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.name} - {teacher.subject || 'No subject specified'}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Grade *</label>
                <select
                  {...register('grade', { required: 'Grade is required' })}
                  className="input"
                >
                  <option value="">Select Grade</option>
                  {grades.map(grade => (
                    <option key={grade} value={grade}>Grade {grade}</option>
                  ))}
                </select>
                {errors.grade && (
                  <p className="mt-1 text-sm text-red-600">{errors.grade.message}</p>
                )}
              </div>

              <div>
                <label className="label">Section *</label>
                <select
                  {...register('section', { required: 'Section is required' })}
                  className="input"
                >
                  <option value="">Select Section</option>
                  {sections.map(section => (
                    <option key={section} value={section}>Section {section}</option>
                  ))}
                </select>
                {errors.section && (
                  <p className="mt-1 text-sm text-red-600">{errors.section.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">Schedule</label>
              <input
                {...register('schedule')}
                className="input"
                placeholder="e.g., Mon, Wed, Fri 9:00-10:00 AM"
              />
            </div>

            <div>
              <label className="label">Virtual Class Link</label>
              <input
                {...register('virtual_link')}
                type="url"
                className="input"
                placeholder="https://meet.google.com/..."
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createClassMutation.isLoading}
                className="btn-primary"
              >
                {createClassMutation.isLoading ? 'Creating...' : 'Create Class'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
