import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { studentsAPI, settingsAPI } from '../../utils/api';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function EditStudentModal({ isOpen, onClose, onSuccess, student }) {
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm();

  // Get settings for grades and sections
  const { data: settingsData } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsAPI.getSettings(),
  });

  // Populate form with student data when modal opens
  useEffect(() => {
    if (student && isOpen) {
      setValue('name', student.name || '');
      setValue('email', student.email || '');
      setValue('student_id', student.student_id || '');
      setValue('grade', student.grade || '');
      setValue('section', student.section || '');
      setValue('phone', student.phone || '');
      setValue('parent_name', student.parent_name || '');
      setValue('parent_phone', student.parent_phone || '');
      setValue('address', student.address || '');
    }
  }, [student, isOpen, setValue]);

  const updateStudentMutation = useMutation({
    mutationFn: (data) => studentsAPI.update(student.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['students']);
      toast.success('Student updated successfully');
      reset();
      onSuccess?.();
    },
    onError: (error) => {
      console.error('Student update error:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          'Failed to update student';
      toast.error(`Error: ${errorMessage}`);
    },
  });

  const onSubmit = (data) => {
    // Clean up the data before sending
    const cleanData = {
      ...data,
      phone: data.phone || null,
      parent_name: data.parent_name || null,
      parent_phone: data.parent_phone || null,
      address: data.address || null
    };
    console.log('Updating student with data:', cleanData);
    updateStudentMutation.mutate(cleanData);
  };

  if (!isOpen || !student) return null;

  const grades = settingsData?.data?.grades || ['9', '10', '11', '12'];
  const sections = settingsData?.data?.sections || ['A', 'B', 'C'];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Edit Student
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="label">Full Name *</label>
              <input
                {...register('name', { required: 'Name is required' })}
                className="input"
                placeholder="Enter student's full name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="label">Email *</label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^\S+@\S+$/i,
                    message: 'Invalid email address',
                  },
                })}
                type="email"
                className="input"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="label">Student ID *</label>
              <input
                {...register('student_id', { required: 'Student ID is required' })}
                className="input"
                placeholder="e.g., STU001"
              />
              {errors.student_id && (
                <p className="mt-1 text-sm text-red-600">{errors.student_id.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Grade *</label>
                <select
                  {...register('grade', { required: 'Grade is required' })}
                  className="input"
                >
                  <option value="">Select Grade</option>
                  {grades.map(grade => (
                    <option key={grade} value={grade}>Grade {grade}</option>
                  ))}
                </select>
                {errors.grade && (
                  <p className="mt-1 text-sm text-red-600">{errors.grade.message}</p>
                )}
              </div>

              <div>
                <label className="label">Section *</label>
                <select
                  {...register('section', { required: 'Section is required' })}
                  className="input"
                >
                  <option value="">Select Section</option>
                  {sections.map(section => (
                    <option key={section} value={section}>Section {section}</option>
                  ))}
                </select>
                {errors.section && (
                  <p className="mt-1 text-sm text-red-600">{errors.section.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">Phone</label>
              <input
                {...register('phone')}
                className="input"
                placeholder="Student's phone number"
              />
            </div>

            <div>
              <label className="label">Parent Name</label>
              <input
                {...register('parent_name')}
                className="input"
                placeholder="Parent/Guardian name"
              />
            </div>

            <div>
              <label className="label">Parent Phone</label>
              <input
                {...register('parent_phone')}
                className="input"
                placeholder="Parent/Guardian phone"
              />
            </div>

            <div>
              <label className="label">Address</label>
              <textarea
                {...register('address')}
                className="input"
                rows="3"
                placeholder="Student's address"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={updateStudentMutation.isPending}
                className="btn-primary"
              >
                {updateStudentMutation.isPending ? 'Updating...' : 'Update Student'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
