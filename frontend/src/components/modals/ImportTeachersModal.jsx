import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { teachersAPI } from '../../utils/api';
import { X, Upload, Download, AlertCircle, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { 
  readFileAsText, 
  csvToArray, 
  generateTeacherSampleCSV, 
  downloadCSV,
  validateTeacherData,
  TEACHER_CSV_HEADERS 
} from '../../utils/csvUtils';

export default function ImportTeachersModal({ isOpen, onClose, onSuccess }) {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState([]);
  const [validationResults, setValidationResults] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const queryClient = useQueryClient();

  const bulkCreateMutation = useMutation({
    mutationFn: (teachers) => teachersAPI.bulkCreate({ teachers }),
    onSuccess: (response) => {
      queryClient.invalidateQueries(['teachers']);
      const { results } = response.data;
      toast.success(`Import completed: ${results.success.length} successful, ${results.failed.length} failed`);
      onSuccess?.(results);
      handleClose();
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Import failed');
    },
  });

  const handleClose = () => {
    setFile(null);
    setCsvData([]);
    setValidationResults([]);
    setIsProcessing(false);
    onClose();
  };

  const handleFileChange = async (event) => {
    const selectedFile = event.target.files[0];
    if (!selectedFile) return;

    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }

    setFile(selectedFile);
    setIsProcessing(true);

    try {
      const content = await readFileAsText(selectedFile);
      const headers = ['name', 'email', 'teacher_id', 'subject', 'phone', 'qualification', 'experience'];
      const data = csvToArray(content, headers);
      
      // Validate each row
      const validationResults = data.map((teacher, index) => ({
        index,
        data: teacher,
        validation: validateTeacherData(teacher)
      }));

      setCsvData(data);
      setValidationResults(validationResults);
    } catch (error) {
      toast.error('Error reading CSV file');
      console.error(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = () => {
    const validTeachers = validationResults
      .filter(result => result.validation.isValid)
      .map(result => ({
        ...result.data,
        password: 'teacher123', // Default password
        experience: result.data.experience ? parseInt(result.data.experience) : 0
      }));

    if (validTeachers.length === 0) {
      toast.error('No valid teachers to import');
      return;
    }

    bulkCreateMutation.mutate(validTeachers);
  };

  const handleDownloadSample = () => {
    const sampleCSV = generateTeacherSampleCSV();
    downloadCSV(sampleCSV, 'teacher_import_sample.csv');
  };

  const validCount = validationResults.filter(r => r.validation.isValid).length;
  const invalidCount = validationResults.filter(r => !r.validation.isValid).length;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={handleClose} />

        <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Import Teachers from CSV</h3>
            <button onClick={handleClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Instructions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Instructions:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Upload a CSV file with teacher data</li>
                <li>• Required columns: Name, Email, Teacher ID, Subject</li>
                <li>• Optional columns: Phone, Qualification, Experience</li>
                <li>• Default password will be set to "teacher123" for all imported teachers</li>
              </ul>
              <button
                onClick={handleDownloadSample}
                className="mt-2 btn-outline text-sm flex items-center space-x-1"
              >
                <Download className="h-4 w-4" />
                <span>Download Sample CSV</span>
              </button>
            </div>

            {/* File Upload */}
            <div>
              <label className="label">Select CSV File</label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500">
                      <span>Upload a file</span>
                      <input
                        type="file"
                        accept=".csv"
                        onChange={handleFileChange}
                        className="sr-only"
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">CSV files only</p>
                </div>
              </div>
              {file && (
                <p className="mt-2 text-sm text-gray-600">
                  Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
                </p>
              )}
            </div>

            {/* Processing */}
            {isProcessing && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-gray-600">Processing CSV file...</span>
              </div>
            )}

            {/* Validation Results */}
            {validationResults.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-gray-900">Validation Results</h4>
                  <div className="flex space-x-4 text-sm">
                    <span className="flex items-center text-green-600">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      {validCount} Valid
                    </span>
                    <span className="flex items-center text-red-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {invalidCount} Invalid
                    </span>
                  </div>
                </div>

                <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Row</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Teacher ID</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Subject</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {validationResults.map((result) => (
                        <tr key={result.index} className={result.validation.isValid ? 'bg-green-50' : 'bg-red-50'}>
                          <td className="px-4 py-2 text-sm text-gray-900">{result.index + 1}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{result.data.name}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{result.data.email}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{result.data.teacher_id}</td>
                          <td className="px-4 py-2 text-sm text-gray-900">{result.data.subject}</td>
                          <td className="px-4 py-2 text-sm">
                            {result.validation.isValid ? (
                              <span className="text-green-600 flex items-center">
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Valid
                              </span>
                            ) : (
                              <div className="text-red-600">
                                <div className="flex items-center">
                                  <AlertCircle className="h-4 w-4 mr-1" />
                                  Invalid
                                </div>
                                <div className="text-xs mt-1">
                                  {result.validation.errors.join(', ')}
                                </div>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button onClick={handleClose} className="btn-outline">
                Cancel
              </button>
              <button
                onClick={handleImport}
                disabled={validCount === 0 || bulkCreateMutation.isLoading}
                className="btn-primary"
              >
                {bulkCreateMutation.isLoading ? 'Importing...' : `Import ${validCount} Teachers`}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
