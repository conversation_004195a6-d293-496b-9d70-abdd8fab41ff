import React, { useState, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { uploadAPI } from '../utils/api';
import { Upload, File, Image, X, CheckCircle, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

export default function FileUpload({ 
  type = 'single', // 'single', 'profile', 'document', 'report'
  accept = '*/*',
  maxSize = 5 * 1024 * 1024, // 5MB
  onSuccess,
  onError,
  className = ''
}) {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const fileInputRef = useRef(null);

  const uploadMutation = useMutation({
    mutationFn: (file) => {
      switch (type) {
        case 'profile':
          return uploadAPI.uploadProfile(file);
        case 'document':
          return uploadAPI.uploadDocument(file);
        case 'report':
          return uploadAPI.uploadReport(file);
        default:
          return uploadAPI.uploadSingle(file);
      }
    },
    onSuccess: (data) => {
      const fileInfo = data.data.file;
      setUploadedFiles(prev => [...prev, fileInfo]);
      toast.success('File uploaded successfully');
      onSuccess?.(fileInfo);
    },
    onError: (error) => {
      const message = error.response?.data?.error || 'Upload failed';
      toast.error(message);
      onError?.(error);
    },
  });

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files) => {
    Array.from(files).forEach(file => {
      // Validate file size
      if (file.size > maxSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB`);
        return;
      }

      // Validate file type if accept is specified
      if (accept !== '*/*') {
        const acceptedTypes = accept.split(',').map(type => type.trim());
        const isValidType = acceptedTypes.some(acceptedType => {
          if (acceptedType.startsWith('.')) {
            return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
          }
          return file.type.match(acceptedType.replace('*', '.*'));
        });

        if (!isValidType) {
          toast.error(`File ${file.name} is not a valid file type`);
          return;
        }
      }

      uploadMutation.mutate(file);
    });
  };

  const onButtonClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith('image/')) {
      return <Image className="h-8 w-8 text-blue-500" />;
    }
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          dragActive
            ? 'border-primary-500 bg-primary-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploadMutation.isLoading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept={accept}
          onChange={handleChange}
          disabled={uploadMutation.isLoading}
        />

        <div className="text-center">
          <Upload className={`mx-auto h-12 w-12 ${
            dragActive ? 'text-primary-500' : 'text-gray-400'
          }`} />
          <div className="mt-4">
            <button
              type="button"
              onClick={onButtonClick}
              disabled={uploadMutation.isLoading}
              className="btn-primary"
            >
              {uploadMutation.isLoading ? 'Uploading...' : 'Choose File'}
            </button>
            <p className="mt-2 text-sm text-gray-600">
              or drag and drop files here
            </p>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Maximum file size: {Math.round(maxSize / 1024 / 1024)}MB
          </p>
        </div>
      </div>

      {/* Upload Progress */}
      {uploadMutation.isLoading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-sm text-blue-800">Uploading file...</span>
          </div>
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">Uploaded Files</h4>
          {uploadedFiles.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {getFileIcon(file.mimetype)}
                <div>
                  <div className="text-sm font-medium text-gray-900">{file.originalName}</div>
                  <div className="text-xs text-gray-500">{formatFileSize(file.size)}</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <button
                  onClick={() => removeFile(index)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error State */}
      {uploadMutation.isError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-3" />
            <span className="text-sm text-red-800">
              {uploadMutation.error?.response?.data?.error || 'Upload failed'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
