import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { reportsAPI } from '../../utils/api';
import { BarChart3, Download, Calendar, Users, Award, FileText, Filter } from 'lucide-react';
import { format } from 'date-fns';

export default function AdminReports() {
  const [reportType, setReportType] = useState('overview');
  const [filters, setFilters] = useState({
    grade: '',
    section: '',
    subject: '',
    startDate: '',
    endDate: '',
    format: 'json'
  });

  const { data: reportData, isLoading, refetch } = useQuery({
    queryKey: ['report', reportType, filters],
    queryFn: () => {
      switch (reportType) {
        case 'attendance':
          return reportsAPI.getAttendance(filters);
        case 'scores':
          return reportsAPI.getScores(filters);
        case 'overview':
          return reportsAPI.getOverview();
        default:
          return reportsAPI.getOverview();
      }
    },
    enabled: reportType === 'overview' || (reportType === 'scores' && filters.subject) || reportType === 'attendance',
  });

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleGenerateReport = () => {
    refetch();
  };

  const handleDownloadCSV = () => {
    const csvFilters = { ...filters, format: 'csv' };
    let downloadUrl = '';
    
    switch (reportType) {
      case 'attendance':
        downloadUrl = `/api/reports/attendance?${new URLSearchParams(csvFilters)}`;
        break;
      case 'scores':
        downloadUrl = `/api/reports/scores?${new URLSearchParams(csvFilters)}`;
        break;
      default:
        return;
    }

    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}${downloadUrl}`;
    link.download = `${reportType}_report_${format(new Date(), 'yyyy-MM-dd')}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const report = reportData?.data;

  const reportTypes = [
    { value: 'overview', label: 'System Overview', icon: BarChart3 },
    { value: 'attendance', label: 'Attendance Report', icon: Calendar },
    { value: 'scores', label: 'Score Report', icon: Award },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
        <p className="text-gray-600">Generate comprehensive reports and analytics</p>
      </div>

      {/* Report Type Selection */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Select Report Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {reportTypes.map((type) => {
            const Icon = type.icon;
            return (
              <button
                key={type.value}
                onClick={() => setReportType(type.value)}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  reportType === type.value
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Icon className={`h-6 w-6 mb-2 ${
                  reportType === type.value ? 'text-primary-600' : 'text-gray-400'
                }`} />
                <div className="font-medium text-gray-900">{type.label}</div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Filters */}
      {(reportType === 'attendance' || reportType === 'scores') && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            <Filter className="h-5 w-5 inline mr-2" />
            Filters
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div>
              <label className="label">Grade</label>
              <select
                className="input"
                value={filters.grade}
                onChange={(e) => handleFilterChange('grade', e.target.value)}
              >
                <option value="">All Grades</option>
                <option value="9">Grade 9</option>
                <option value="10">Grade 10</option>
                <option value="11">Grade 11</option>
                <option value="12">Grade 12</option>
              </select>
            </div>
            <div>
              <label className="label">Section</label>
              <select
                className="input"
                value={filters.section}
                onChange={(e) => handleFilterChange('section', e.target.value)}
              >
                <option value="">All Sections</option>
                <option value="A">Section A</option>
                <option value="B">Section B</option>
                <option value="C">Section C</option>
              </select>
            </div>
            {reportType === 'scores' && (
              <div>
                <label className="label">Subject *</label>
                <select
                  className="input"
                  value={filters.subject}
                  onChange={(e) => handleFilterChange('subject', e.target.value)}
                >
                  <option value="">Select Subject</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="English">English</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Biology">Biology</option>
                </select>
              </div>
            )}
            <div>
              <label className="label">Start Date</label>
              <input
                type="date"
                className="input"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>
            <div>
              <label className="label">End Date</label>
              <input
                type="date"
                className="input"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>
          </div>
          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-gray-500">
              {reportType === 'scores' && !filters.subject && (
                <span className="text-red-500">* Subject is required for score reports</span>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleGenerateReport}
                disabled={isLoading || (reportType === 'scores' && !filters.subject)}
                className="btn-primary"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                {isLoading ? 'Generating...' : 'Generate Report'}
              </button>
              {report && (reportType === 'attendance' || reportType === 'scores') && (
                <button
                  onClick={handleDownloadCSV}
                  className="btn-outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download CSV
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Report Results */}
      {isLoading ? (
        <div className="card">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mr-3"></div>
            <span>Generating report...</span>
          </div>
        </div>
      ) : report ? (
        <div className="space-y-6">
          {/* Report Header */}
          <div className="card">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{report.title}</h3>
                <p className="text-sm text-gray-500">
                  Generated on {format(new Date(report.generatedAt), 'PPpp')} by {report.generatedBy}
                </p>
              </div>
              <FileText className="h-8 w-8 text-gray-400" />
            </div>
          </div>

          {/* Summary Statistics */}
          {report.summary && (
            <div className="card">
              <h4 className="text-md font-medium text-gray-900 mb-4">Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(report.summary).map(([key, value]) => (
                  <div key={key} className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">{value}</div>
                    <div className="text-sm text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Data Table */}
          {report.data && Array.isArray(report.data) && report.data.length > 0 && (
            <div className="card overflow-hidden">
              <h4 className="text-md font-medium text-gray-900 mb-4">Detailed Data</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {Object.keys(report.data[0]).map((key) => (
                        <th
                          key={key}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {report.data.slice(0, 50).map((row, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {typeof value === 'number' && value % 1 !== 0 
                              ? value.toFixed(2) 
                              : value?.toString() || '-'
                            }
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
                {report.data.length > 50 && (
                  <div className="px-6 py-3 bg-gray-50 text-sm text-gray-500">
                    Showing first 50 of {report.data.length} records. Download CSV for complete data.
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Additional Data Sections */}
          {report.userStats && (
            <div className="card">
              <h4 className="text-md font-medium text-gray-900 mb-4">User Statistics</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {report.userStats.map((stat) => (
                  <div key={stat.role} className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">{stat.count}</div>
                    <div className="text-sm text-gray-500 capitalize">{stat.role}s</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {report.gradeStats && (
            <div className="card">
              <h4 className="text-md font-medium text-gray-900 mb-4">Grade Distribution</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {report.gradeStats.map((grade) => (
                  <div key={`${grade.grade}-${grade.section}`} className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">
                      Grade {grade.grade} - {grade.section}
                    </div>
                    <div className="text-sm text-gray-500">{grade.student_count} students</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="card">
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">Select a report type and configure filters to generate a report</div>
          </div>
        </div>
      )}
    </div>
  );
}
