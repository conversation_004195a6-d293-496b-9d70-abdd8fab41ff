import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { attendanceAPI, studentsAPI } from '../../utils/api';
import { Calendar, CheckCircle, XCircle, Clock, Filter, TrendingUp } from 'lucide-react';

export default function StudentAttendance() {
  const { user } = useAuth();
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Get student profile first
  const { data: studentData } = useQuery({
    queryKey: ['student-profile', user?.id],
    queryFn: async () => {
      if (user?.role === 'student') {
        const response = await studentsAPI.getAll();
        return response.data.students.find(s => s.user_id === user.id);
      }
      return null;
    },
    enabled: !!user?.id && user?.role === 'student',
  });

  const studentId = studentData?.id;

  const { data: attendanceData, isLoading } = useQuery({
    queryKey: ['student-attendance', studentId, selectedMonth, selectedYear],
    queryFn: () => attendanceAPI.getByStudent(studentId, {
      month: selectedMonth + 1,
      year: selectedYear
    }),
    enabled: !!studentId,
  });

  const attendance = attendanceData?.data?.attendance || [];

  // Calculate statistics
  const totalDays = attendance.length;
  const presentDays = attendance.filter(a => a.status === 'present').length;
  const absentDays = attendance.filter(a => a.status === 'absent').length;
  const lateDays = attendance.filter(a => a.status === 'late').length;
  const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;

  // Group attendance by date
  const attendanceByDate = attendance.reduce((acc, record) => {
    const date = new Date(record.date).toDateString();
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(record);
    return acc;
  }, {});

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">My Attendance</h1>
        <p className="text-gray-600">Track your attendance record and statistics</p>
      </div>

      {/* Filters */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Filter className="h-5 w-5 mr-2" />
          Filter by Period
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">Month</label>
            <select
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
              className="input"
            >
              {months.map((month, index) => (
                <option key={index} value={index}>
                  {month}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="label">Year</label>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="input"
            >
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{totalDays}</div>
              <div className="text-sm text-gray-500">Total Days</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{presentDays}</div>
              <div className="text-sm text-gray-500">Present</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <XCircle className="h-8 w-8 text-red-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{absentDays}</div>
              <div className="text-sm text-gray-500">Absent</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{attendancePercentage}%</div>
              <div className="text-sm text-gray-500">Attendance Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Records */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Attendance Records - {months[selectedMonth]} {selectedYear}
        </h3>
        
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : Object.keys(attendanceByDate).length > 0 ? (
          <div className="space-y-4">
            {Object.entries(attendanceByDate)
              .sort(([a], [b]) => new Date(b) - new Date(a))
              .map(([date, records]) => (
                <div key={date} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">
                      {new Date(date).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </h4>
                    <div className="flex items-center space-x-2">
                      {records.map((record, index) => (
                        <span
                          key={index}
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            record.status === 'present' ? 'bg-green-100 text-green-800' :
                            record.status === 'late' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}
                        >
                          {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    {records.map((record, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-3">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                            record.status === 'present' ? 'bg-green-100' :
                            record.status === 'late' ? 'bg-yellow-100' : 'bg-red-100'
                          }`}>
                            {record.status === 'present' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : record.status === 'late' ? (
                              <Clock className="h-4 w-4 text-yellow-600" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{record.class_name}</div>
                            <div className="text-gray-500">{record.subject}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-gray-900">
                            {new Date(record.created_at).toLocaleTimeString()}
                          </div>
                          {record.notes && (
                            <div className="text-gray-500 text-xs">{record.notes}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No attendance records</h3>
            <p className="mt-1 text-sm text-gray-500">
              No attendance has been marked for {months[selectedMonth]} {selectedYear}.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
