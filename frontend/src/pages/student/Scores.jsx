import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { scoresAPI, studentsAPI } from '../../utils/api';
import { Award, TrendingUp, BarChart3, Filter, Calendar } from 'lucide-react';

export default function StudentScores() {
  const { user } = useAuth();
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedMonth, setSelectedMonth] = useState('');

  // Get student profile first
  const { data: studentData } = useQuery({
    queryKey: ['student-profile', user?.id],
    queryFn: async () => {
      if (user?.role === 'student') {
        const response = await studentsAPI.getAll();
        return response.data.students.find(s => s.user_id === user.id);
      }
      return null;
    },
    enabled: !!user?.id && user?.role === 'student',
  });

  const studentId = studentData?.id;

  const { data: scoresData, isLoading } = useQuery({
    queryKey: ['student-scores', studentId, selectedSubject, selectedMonth],
    queryFn: () => scoresAPI.getByStudent(studentId, {
      subject: selectedSubject,
      month: selectedMonth
    }),
    enabled: !!studentId,
  });

  const scores = scoresData?.data?.scores || [];

  // Calculate statistics
  const totalTests = scores.length;
  const averageScore = totalTests > 0 ? Math.round(scores.reduce((sum, score) => sum + score.score, 0) / totalTests) : 0;
  const highestScore = totalTests > 0 ? Math.max(...scores.map(s => s.score)) : 0;
  const lowestScore = totalTests > 0 ? Math.min(...scores.map(s => s.score)) : 0;

  // Get unique subjects
  const subjects = [...new Set(scores.map(score => score.subject))];

  // Group scores by subject
  const scoresBySubject = scores.reduce((acc, score) => {
    if (!acc[score.subject]) {
      acc[score.subject] = [];
    }
    acc[score.subject].push(score);
    return acc;
  }, {});

  const months = [
    { value: '', label: 'All Months' },
    { value: '1', label: 'January' },
    { value: '2', label: 'February' },
    { value: '3', label: 'March' },
    { value: '4', label: 'April' },
    { value: '5', label: 'May' },
    { value: '6', label: 'June' },
    { value: '7', label: 'July' },
    { value: '8', label: 'August' },
    { value: '9', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ];

  const getGradeColor = (score) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 80) return 'text-blue-600 bg-blue-100';
    if (score >= 70) return 'text-yellow-600 bg-yellow-100';
    if (score >= 60) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getGradeLetter = (score) => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">My Scores</h1>
        <p className="text-gray-600">Track your test scores and academic performance</p>
      </div>

      {/* Filters */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Filter className="h-5 w-5 mr-2" />
          Filter Scores
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">Subject</label>
            <select
              value={selectedSubject}
              onChange={(e) => setSelectedSubject(e.target.value)}
              className="input"
            >
              <option value="">All Subjects</option>
              {subjects.map((subject) => (
                <option key={subject} value={subject}>
                  {subject}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="label">Month</label>
            <select
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="input"
            >
              {months.map((month) => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{totalTests}</div>
              <div className="text-sm text-gray-500">Total Tests</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{averageScore}%</div>
              <div className="text-sm text-gray-500">Average Score</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{highestScore}%</div>
              <div className="text-sm text-gray-500">Highest Score</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-orange-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{getGradeLetter(averageScore)}</div>
              <div className="text-sm text-gray-500">Overall Grade</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scores by Subject */}
      {Object.keys(scoresBySubject).length > 0 ? (
        <div className="space-y-6">
          {Object.entries(scoresBySubject).map(([subject, subjectScores]) => {
            const subjectAverage = Math.round(subjectScores.reduce((sum, score) => sum + score.score, 0) / subjectScores.length);
            
            return (
              <div key={subject} className="card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{subject}</h3>
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-500">
                      Average: <span className="font-medium text-gray-900">{subjectAverage}%</span>
                    </span>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(subjectAverage)}`}>
                      {getGradeLetter(subjectAverage)}
                    </span>
                  </div>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Test Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Score
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Grade
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Teacher
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {subjectScores
                        .sort((a, b) => new Date(b.test_date) - new Date(a.test_date))
                        .map((score, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {score.test_name || 'Test'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(score.test_date).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-gray-900 mr-2">
                                  {score.score}%
                                </span>
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full ${
                                      score.score >= 90 ? 'bg-green-500' :
                                      score.score >= 80 ? 'bg-blue-500' :
                                      score.score >= 70 ? 'bg-yellow-500' :
                                      score.score >= 60 ? 'bg-orange-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${score.score}%` }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(score.score)}`}>
                                {getGradeLetter(score.score)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {score.teacher_name}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="card">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Award className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No scores available</h3>
              <p className="mt-1 text-sm text-gray-500">
                Your test scores will appear here once teachers add them.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
