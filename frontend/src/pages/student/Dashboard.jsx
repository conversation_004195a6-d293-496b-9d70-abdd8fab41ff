import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { studentsAPI, attendanceAPI } from '../../utils/api';
import { Calendar, Award, BookOpen, TrendingUp, Clock, CheckCircle, XCircle } from 'lucide-react';

export default function StudentDashboard() {
  const { user } = useAuth();
  const studentId = user?.student?.id;

  const { data: studentData } = useQuery({
    queryKey: ['student', studentId],
    queryFn: () => studentsAPI.getById(studentId),
    enabled: !!studentId,
  });

  const { data: attendanceData } = useQuery({
    queryKey: ['student-attendance', studentId],
    queryFn: () => attendanceAPI.getByStudent(studentId),
    enabled: !!studentId,
  });

  const student = studentData?.data?.student;
  const stats = studentData?.data?.stats;
  const attendance = attendanceData?.data?.attendance || [];

  // Calculate attendance percentage
  const totalDays = attendance.length;
  const presentDays = attendance.filter(a => a.status === 'present').length;
  const attendancePercentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;

  // Recent attendance (last 7 days)
  const recentAttendance = attendance.slice(0, 7);

  const quickStats = [
    {
      name: 'Attendance Rate',
      value: `${attendancePercentage}%`,
      icon: Calendar,
      color: attendancePercentage >= 90 ? 'bg-green-500' : attendancePercentage >= 75 ? 'bg-yellow-500' : 'bg-red-500',
      description: `${presentDays}/${totalDays} days present`,
    },
    {
      name: 'Average Score',
      value: stats?.average_score ? `${Math.round(stats.average_score)}%` : 'N/A',
      icon: Award,
      color: 'bg-blue-500',
      description: 'Across all subjects',
    },
    {
      name: 'Active Classes',
      value: stats?.total_classes || '0',
      icon: BookOpen,
      color: 'bg-purple-500',
      description: 'This semester',
    },
    {
      name: 'Class Rank',
      value: stats?.class_rank ? `#${stats.class_rank}` : 'N/A',
      icon: TrendingUp,
      color: 'bg-orange-500',
      description: 'In your class',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Welcome back, {user?.name}!</h1>
        <p className="text-gray-600">
          Grade {user?.student?.grade} - Section {user?.student?.section} • Student ID: {user?.student?.student_id}
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {quickStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} p-3 rounded-lg`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd>
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className="text-xs text-gray-500">
                        {stat.description}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Attendance */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Attendance</h3>
          <div className="space-y-3">
            {recentAttendance.length > 0 ? (
              recentAttendance.map((record, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      record.status === 'present' ? 'bg-green-100' :
                      record.status === 'late' ? 'bg-yellow-100' : 'bg-red-100'
                    }`}>
                      {record.status === 'present' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : record.status === 'late' ? (
                        <Clock className="h-5 w-5 text-yellow-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(record.date).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">{record.class_name}</div>
                    </div>
                  </div>
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                    record.status === 'present' ? 'bg-green-100 text-green-800' :
                    record.status === 'late' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                No attendance records found
              </div>
            )}
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            {student?.classes && student.classes.length > 0 ? (
              student.classes.map((classItem, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium text-blue-900">{classItem.subject}</div>
                    <div className="text-xs text-blue-700">
                      {classItem.room} • {classItem.teacher_name}
                    </div>
                  </div>
                  <div className="text-xs text-blue-600">{classItem.time}</div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                <BookOpen className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p>No classes scheduled for today</p>
                <p className="text-xs">Check back later for updates</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Scores */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Test Scores</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {student?.recent_scores && student.recent_scores.length > 0 ? (
            student.recent_scores.slice(0, 3).map((score, index) => (
              <div key={index} className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-blue-900">{score.subject}</div>
                    <div className="text-xs text-blue-700">
                      {new Date(score.test_date).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-lg font-bold text-blue-600">{score.score}%</div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-3 text-center text-gray-500 py-8">
              <Award className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p>No test scores available</p>
              <p className="text-xs">Scores will appear here once tests are graded</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn-outline text-left p-4">
            <Calendar className="h-6 w-6 text-blue-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">View Attendance</div>
            <div className="text-xs text-gray-500">Check your attendance record</div>
          </button>
          <button className="btn-outline text-left p-4">
            <Award className="h-6 w-6 text-green-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">View Scores</div>
            <div className="text-xs text-gray-500">Check your test results</div>
          </button>
          <button className="btn-outline text-left p-4">
            <BookOpen className="h-6 w-6 text-purple-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">Class Schedule</div>
            <div className="text-xs text-gray-500">View your timetable</div>
          </button>
          <button className="btn-outline text-left p-4">
            <TrendingUp className="h-6 w-6 text-orange-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">Performance</div>
            <div className="text-xs text-gray-500">View your progress</div>
          </button>
        </div>
      </div>
    </div>
  );
}
