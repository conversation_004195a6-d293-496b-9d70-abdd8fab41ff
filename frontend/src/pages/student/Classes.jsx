import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI, studentsAPI } from '../../utils/api';
import { BookOpen, Clock, MapPin, User, Calendar } from 'lucide-react';

export default function StudentClasses() {
  const { user } = useAuth();

  // Get student profile first
  const { data: studentData } = useQuery({
    queryKey: ['student-profile', user?.id],
    queryFn: async () => {
      if (user?.role === 'student') {
        const response = await studentsAPI.getAll();
        return response.data.students.find(s => s.user_id === user.id);
      }
      return null;
    },
    enabled: !!user?.id && user?.role === 'student',
  });

  const { data: classesData, isLoading } = useQuery({
    queryKey: ['student-classes', studentData?.grade, studentData?.section],
    queryFn: () => classesAPI.getAll({
      grade: studentData?.grade,
      section: studentData?.section
    }),
    enabled: !!studentData?.grade && !!studentData?.section,
  });

  const classes = classesData?.data?.classes || [];

  // Group classes by day of week
  const classesByDay = classes.reduce((acc, classItem) => {
    const day = classItem.day_of_week || 'Monday';
    if (!acc[day]) {
      acc[day] = [];
    }
    acc[day].push(classItem);
    return acc;
  }, {});

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

  const getTimeSlotColor = (time) => {
    const hour = parseInt(time?.split(':')[0] || '9');
    if (hour < 10) return 'bg-blue-50 border-blue-200';
    if (hour < 12) return 'bg-green-50 border-green-200';
    if (hour < 14) return 'bg-yellow-50 border-yellow-200';
    if (hour < 16) return 'bg-purple-50 border-purple-200';
    return 'bg-orange-50 border-orange-200';
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">My Classes</h1>
        <p className="text-gray-600">
          View your class schedule and teacher information
          {studentData && (
            <span className="ml-2 text-primary-600 font-medium">
              Grade {studentData.grade} - Section {studentData.section}
            </span>
          )}
        </p>
      </div>

      {/* Class Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{classes.length}</div>
              <div className="text-sm text-gray-500">Total Classes</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <User className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {[...new Set(classes.map(c => c.teacher_name))].length}
              </div>
              <div className="text-sm text-gray-500">Teachers</div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {Object.keys(classesByDay).length}
              </div>
              <div className="text-sm text-gray-500">Active Days</div>
            </div>
          </div>
        </div>
      </div>

      {/* Weekly Schedule */}
      {isLoading ? (
        <div className="card">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        </div>
      ) : classes.length > 0 ? (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Weekly Schedule</h3>
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
            {daysOfWeek.map((day) => (
              <div key={day} className="space-y-3">
                <h4 className="text-sm font-medium text-gray-900 text-center py-2 bg-gray-50 rounded-lg">
                  {day}
                </h4>
                <div className="space-y-2">
                  {classesByDay[day] ? (
                    classesByDay[day]
                      .sort((a, b) => (a.start_time || '09:00').localeCompare(b.start_time || '09:00'))
                      .map((classItem, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border-2 ${getTimeSlotColor(classItem.start_time)}`}
                        >
                          <div className="text-sm font-medium text-gray-900 mb-1">
                            {classItem.subject}
                          </div>
                          <div className="text-xs text-gray-600 space-y-1">
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {classItem.start_time || '09:00'} - {classItem.end_time || '10:00'}
                            </div>
                            <div className="flex items-center">
                              <MapPin className="h-3 w-3 mr-1" />
                              {classItem.room || 'Room TBA'}
                            </div>
                            <div className="flex items-center">
                              <User className="h-3 w-3 mr-1" />
                              {classItem.teacher_name || 'Teacher TBA'}
                            </div>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-4 text-gray-400 text-xs">
                      No classes
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="text-center py-8">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No classes scheduled</h3>
            <p className="mt-1 text-sm text-gray-500">
              Your class schedule will appear here once classes are assigned.
            </p>
          </div>
        </div>
      )}

      {/* All Classes List */}
      {classes.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">All Classes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {classes.map((classItem, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{classItem.subject}</h4>
                    <p className="text-xs text-gray-500">{classItem.name}</p>
                  </div>
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800">
                    {classItem.day_of_week || 'TBA'}
                  </span>
                </div>
                
                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex items-center">
                    <User className="h-3 w-3 mr-2" />
                    <span>{classItem.teacher_name || 'Teacher TBA'}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-2" />
                    <span>
                      {classItem.start_time || '09:00'} - {classItem.end_time || '10:00'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-2" />
                    <span>{classItem.room || 'Room TBA'}</span>
                  </div>
                  {classItem.description && (
                    <div className="mt-2 text-xs text-gray-500">
                      {classItem.description}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
