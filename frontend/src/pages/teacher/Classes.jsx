import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI } from '../../utils/api';
import { BookOpen, Users, Calendar, ExternalLink } from 'lucide-react';

export default function TeacherClasses() {
  const { user } = useAuth();
  const teacherId = user?.teacher?.id;

  const { data: classesData, isLoading } = useQuery({
    queryKey: ['teacher-classes', teacherId],
    queryFn: () => classesAPI.getByTeacher(teacherId),
    enabled: !!teacherId,
  });

  const classes = classesData?.data?.classes || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Classes</h1>
          <p className="text-gray-600">Manage your assigned classes</p>
        </div>
      </div>

      {/* Classes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {classes.map((classItem) => (
          <div key={classItem.id} className="card hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">{classItem.name}</h3>
                  <p className="text-sm text-gray-500">{classItem.subject}</p>
                </div>
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <div className="flex items-center text-sm text-gray-600">
                <Users className="h-4 w-4 mr-2" />
                Grade {classItem.grade} - Section {classItem.section}
                {classItem.student_count && (
                  <span className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded-full">
                    {classItem.student_count} students
                  </span>
                )}
              </div>
              
              {classItem.schedule && (
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  {classItem.schedule}
                </div>
              )}

              {classItem.virtual_link && (
                <div className="mt-3">
                  <a
                    href={classItem.virtual_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors"
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Join Virtual Class
                  </a>
                </div>
              )}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <button className="btn-outline text-xs">
                  View Students
                </button>
                <button className="btn-outline text-xs">
                  Mark Attendance
                </button>
                <button className="btn-outline text-xs">
                  Add Scores
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {classes.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500">No classes assigned</div>
          <p className="text-sm text-gray-400 mt-2">
            Contact your administrator to get classes assigned to you
          </p>
        </div>
      )}
    </div>
  );
}
