import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI, scoresAPI, studentsAPI } from '../../utils/api';
import { Award, Plus, Edit, Trash2, BarChart3 } from 'lucide-react';
import toast from 'react-hot-toast';
import AddScoreModal from '../../components/modals/AddScoreModal';

export default function TeacherScores() {
  const { user } = useAuth();
  const [selectedClass, setSelectedClass] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingScore, setEditingScore] = useState(null);
  const queryClient = useQueryClient();

  const teacherId = user?.teacher?.id;

  const { data: classesData } = useQuery({
    queryKey: ['teacher-classes', teacherId],
    queryFn: () => classesAPI.getByTeacher(teacherId),
    enabled: !!teacherId,
  });

  const { data: scoresData } = useQuery({
    queryKey: ['teacher-scores', user?.teacher?.id, selectedClass],
    queryFn: () => {
      const classInfo = classes.find(c => c.id.toString() === selectedClass);
      return scoresAPI.getByTeacher(user?.teacher?.id, {
        grade: classInfo?.grade,
        section: classInfo?.section
      });
    },
    enabled: !!user?.teacher?.id && !!selectedClass,
  });

  const deleteScoreMutation = useMutation({
    mutationFn: scoresAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries(['teacher-scores']);
      toast.success('Score deleted successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to delete score');
    },
  });

  const classes = classesData?.data?.classes || [];
  const scores = scoresData?.data?.scores || [];

  const handleDeleteScore = async (id) => {
    if (window.confirm('Are you sure you want to delete this score?')) {
      deleteScoreMutation.mutate(id);
    }
  };

  const groupedScores = scores.reduce((acc, score) => {
    const key = `${score.subject}-${score.type}`;
    if (!acc[key]) {
      acc[key] = {
        subject: score.subject,
        type: score.type,
        scores: []
      };
    }
    acc[key].scores.push(score);
    return acc;
  }, {});

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Score Management</h1>
          <p className="text-gray-600">Manage student test scores and grades</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary"
          disabled={!selectedClass}
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Score
        </button>
      </div>

      {/* Controls */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">Select Class</label>
            <select
              className="input"
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
            >
              <option value="">Choose a class...</option>
              {classes.map((classItem) => (
                <option key={classItem.id} value={classItem.id}>
                  {classItem.name} - Grade {classItem.grade}{classItem.section}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              disabled={!selectedClass}
              className="btn-outline w-full"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              View Analytics
            </button>
          </div>
        </div>
      </div>

      {/* Scores by Subject/Type */}
      {selectedClass && Object.keys(groupedScores).length > 0 ? (
        <div className="space-y-6">
          {Object.values(groupedScores).map((group) => (
            <div key={`${group.subject}-${group.type}`} className="card">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {group.subject} - {group.type.charAt(0).toUpperCase() + group.type.slice(1)}
                </h3>
                <div className="text-sm text-gray-500">
                  {group.scores.length} scores
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Percentage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Test Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {group.scores.map((score) => {
                      const percentage = Math.round((score.score / score.max_score) * 100);
                      return (
                        <tr key={score.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-8 w-8 flex-shrink-0">
                                <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                                  <span className="text-xs font-medium text-primary-600">
                                    {score.student_name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-3">
                                <div className="text-sm font-medium text-gray-900">{score.student_name}</div>
                                <div className="text-xs text-gray-500">ID: {score.student_id}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {score.score}/{score.max_score}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              percentage >= 90 ? 'bg-green-100 text-green-800' :
                              percentage >= 80 ? 'bg-blue-100 text-blue-800' :
                              percentage >= 70 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {percentage}%
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(score.test_date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {score.description || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => setEditingScore(score)}
                                className="text-primary-600 hover:text-primary-900"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteScore(score.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      ) : selectedClass ? (
        <div className="card">
          <div className="text-center py-12">
            <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">No scores found for this class</div>
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary mt-4"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add First Score
            </button>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="text-center py-12">
            <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">Please select a class to manage scores</div>
          </div>
        </div>
      )}

      {/* Add/Edit Score Modal */}
      {(showAddModal || editingScore) && (
        <AddScoreModal
          isOpen={showAddModal || !!editingScore}
          onClose={() => {
            setShowAddModal(false);
            setEditingScore(null);
          }}
          score={editingScore}
          classId={selectedClass}
          onSuccess={() => {
            setShowAddModal(false);
            setEditingScore(null);
            queryClient.invalidateQueries(['teacher-scores']);
          }}
        />
      )}
    </div>
  );
}
