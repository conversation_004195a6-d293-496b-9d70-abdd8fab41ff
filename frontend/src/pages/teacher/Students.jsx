import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { studentsAPI, classesAPI } from '../../utils/api';
import { Users, Search, Eye, BookOpen } from 'lucide-react';

export default function TeacherStudents() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');

  const teacherId = user?.teacher?.id;

  const { data: classesData } = useQuery({
    queryKey: ['teacher-classes', teacherId],
    queryFn: () => classesAPI.getByTeacher(teacherId),
    enabled: !!teacherId,
  });

  const { data: studentsData } = useQuery({
    queryKey: ['students', selectedClass],
    queryFn: () => {
      if (selectedClass) {
        const classInfo = classesData?.data?.classes?.find(c => c.id.toString() === selectedClass);
        if (classInfo) {
          return studentsAPI.getAll({ grade: classInfo.grade, section: classInfo.section });
        }
      }
      return studentsAPI.getAll();
    },
    enabled: !!classesData,
  });

  const classes = classesData?.data?.classes || [];
  const students = studentsData?.data?.students || [];

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Students</h1>
          <p className="text-gray-600">View and manage your students</p>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search students..."
              className="input pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="input"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            <option value="">All Classes</option>
            {classes.map((classItem) => (
              <option key={classItem.id} value={classItem.id}>
                {classItem.name}
              </option>
            ))}
          </select>
          <div className="text-sm text-gray-500 flex items-center">
            Total: {filteredStudents.length} students
          </div>
        </div>
      </div>

      {/* Students Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStudents.map((student) => (
          <div key={student.id} className="card hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">{student.name}</h3>
                  <p className="text-sm text-gray-500">{student.student_id}</p>
                </div>
              </div>
              <button className="text-primary-600 hover:text-primary-900">
                <Eye className="h-4 w-4" />
              </button>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <BookOpen className="h-4 w-4 mr-2" />
                Grade {student.grade} - Section {student.section}
              </div>
              
              <div className="text-sm text-gray-600">
                <span className="font-medium">Email:</span> {student.email}
              </div>
              
              {student.phone && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Phone:</span> {student.phone}
                </div>
              )}

              {student.parent_name && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Parent:</span> {student.parent_name}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredStudents.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500">No students found</div>
          {selectedClass && (
            <p className="text-sm text-gray-400 mt-2">
              Try selecting a different class or clearing the search
            </p>
          )}
        </div>
      )}
    </div>
  );
}
