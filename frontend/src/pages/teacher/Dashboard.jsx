import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI } from '../../utils/api';
import { Users, BookOpen, Calendar, Award } from 'lucide-react';

export default function TeacherDashboard() {
  const { user } = useAuth();
  const teacherId = user?.teacher?.id;

  // Get teacher's classes
  const { data: classesData, isLoading: classesLoading } = useQuery({
    queryKey: ['teacher-classes', teacherId],
    queryFn: () => classesAPI.getByTeacher(teacherId),
    enabled: !!teacherId,
  });

  const classes = classesData?.classes || [];

  // Calculate stats
  const totalStudents = classes.reduce((sum, cls) => sum + (cls.student_count || 0), 0);
  const totalClasses = classes.length;

  const stats = [
    {
      name: 'Total Classes',
      value: totalClasses.toString(),
      icon: BookOpen,
      color: 'bg-blue-500',
      description: 'Classes assigned',
    },
    {
      name: 'Total Students',
      value: totalStudents.toString(),
      icon: Users,
      color: 'bg-green-500',
      description: 'Students enrolled',
    },
    {
      name: 'Today\'s Classes',
      value: '0',
      icon: Calendar,
      color: 'bg-purple-500',
      description: 'Scheduled today',
    },
    {
      name: 'Pending Scores',
      value: '0',
      icon: Award,
      color: 'bg-orange-500',
      description: 'Tests to grade',
    },
  ];

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-500">Loading user data...</div>
        </div>
      </div>
    );
  }

  if (!user.teacher) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-2">Teacher profile not found</div>
          <div className="text-sm text-gray-500">Please contact administrator</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user.name}!
        </h1>
        <p className="text-gray-600">Here's what's happening with your classes today.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500">{stat.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Classes Overview */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Your Classes</h3>
          {classesLoading && (
            <div className="text-sm text-gray-500">Loading...</div>
          )}
        </div>

        {classesLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : classes.length === 0 ? (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Classes Assigned</h3>
            <p className="text-gray-600">
              You don't have any classes assigned yet. Contact your administrator to get started.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {classes.map((cls) => (
              <div key={cls.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{cls.name}</h4>
                    <p className="text-sm text-gray-600">
                      Grade {cls.grade}{cls.section} • {cls.subject}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {cls.student_count || 0} students
                    </p>
                    {cls.schedule && (
                      <p className="text-xs text-gray-500 mt-1">{cls.schedule}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Calendar className="w-6 h-6 text-blue-500 mb-2" />
            <h4 className="font-medium text-gray-900">Mark Attendance</h4>
            <p className="text-sm text-gray-600">Record student attendance for today</p>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Award className="w-6 h-6 text-green-500 mb-2" />
            <h4 className="font-medium text-gray-900">Grade Assignments</h4>
            <p className="text-sm text-gray-600">Review and grade student work</p>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Users className="w-6 h-6 text-purple-500 mb-2" />
            <h4 className="font-medium text-gray-900">View Students</h4>
            <p className="text-sm text-gray-600">See all students in your classes</p>
          </button>
        </div>
      </div>

      {/* Teacher Info */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Teacher Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600">Name</p>
            <p className="text-gray-900">{user.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Email</p>
            <p className="text-gray-900">{user.email}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Subject</p>
            <p className="text-gray-900">{user.teacher.subject}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Experience</p>
            <p className="text-gray-900">{user.teacher.experience} years</p>
          </div>
        </div>
      </div>
    </div>
  );
}