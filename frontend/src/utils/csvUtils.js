/**
 * CSV Import/Export Utilities
 */

/**
 * Convert array of objects to CSV string
 * @param {Array} data - Array of objects
 * @param {Array} headers - Array of header objects with key and label
 * @returns {string} CSV string
 */
export function arrayToCSV(data, headers) {
  if (!data || data.length === 0) return '';

  // Create header row
  const headerRow = headers.map(header => `"${header.label}"`).join(',');
  
  // Create data rows
  const dataRows = data.map(item => {
    return headers.map(header => {
      const value = item[header.key] || '';
      // Escape quotes and wrap in quotes
      return `"${String(value).replace(/"/g, '""')}"`;
    }).join(',');
  });

  return [headerRow, ...dataRows].join('\n');
}

/**
 * Parse CSV string to array of objects
 * @param {string} csvString - CSV content
 * @param {Array} headers - Array of header keys
 * @returns {Array} Array of objects
 */
export function csvToArray(csvString, headers) {
  if (!csvString.trim()) return [];

  const lines = csvString.trim().split('\n');
  const result = [];

  // Skip header row (first line)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    const values = parseCSVLine(line);
    const obj = {};

    headers.forEach((header, index) => {
      obj[header] = values[index] || '';
    });

    result.push(obj);
  }

  return result;
}

/**
 * Parse a single CSV line handling quoted values
 * @param {string} line - CSV line
 * @returns {Array} Array of values
 */
function parseCSVLine(line) {
  const result = [];
  let current = '';
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];
    const nextChar = line[i + 1];

    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current);
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current);
  return result;
}

/**
 * Download CSV file
 * @param {string} csvContent - CSV content
 * @param {string} filename - File name
 */
export function downloadCSV(csvContent, filename) {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

/**
 * Read file as text
 * @param {File} file - File object
 * @returns {Promise<string>} File content
 */
export function readFileAsText(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(e);
    reader.readAsText(file);
  });
}

/**
 * Student CSV headers configuration
 */
export const STUDENT_CSV_HEADERS = [
  { key: 'name', label: 'Full Name' },
  { key: 'email', label: 'Email' },
  { key: 'student_id', label: 'Student ID' },
  { key: 'grade', label: 'Grade' },
  { key: 'section', label: 'Section' },
  { key: 'phone', label: 'Phone' },
  { key: 'parent_name', label: 'Parent Name' },
  { key: 'parent_phone', label: 'Parent Phone' },
  { key: 'address', label: 'Address' }
];

/**
 * Teacher CSV headers configuration
 */
export const TEACHER_CSV_HEADERS = [
  { key: 'name', label: 'Full Name' },
  { key: 'email', label: 'Email' },
  { key: 'teacher_id', label: 'Teacher ID' },
  { key: 'subject', label: 'Subject' },
  { key: 'phone', label: 'Phone' },
  { key: 'qualification', label: 'Qualification' },
  { key: 'experience', label: 'Experience (Years)' }
];

/**
 * Generate sample CSV content for students
 */
export function generateStudentSampleCSV() {
  const sampleData = [
    {
      name: 'John Doe',
      email: '<EMAIL>',
      student_id: 'STU001',
      grade: '10',
      section: 'A',
      phone: '******-0001',
      parent_name: 'Jane Doe',
      parent_phone: '******-0002',
      address: '123 Main St, City, State 12345'
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      student_id: 'STU002',
      grade: '10',
      section: 'B',
      phone: '******-0003',
      parent_name: 'Bob Smith',
      parent_phone: '******-0004',
      address: '456 Oak Ave, City, State 12345'
    }
  ];

  return arrayToCSV(sampleData, STUDENT_CSV_HEADERS);
}

/**
 * Generate sample CSV content for teachers
 */
export function generateTeacherSampleCSV() {
  const sampleData = [
    {
      name: 'Dr. Sarah Johnson',
      email: '<EMAIL>',
      teacher_id: 'TCH001',
      subject: 'Mathematics',
      phone: '******-1001',
      qualification: 'Ph.D. Mathematics',
      experience: '10'
    },
    {
      name: 'Mr. Michael Brown',
      email: '<EMAIL>',
      teacher_id: 'TCH002',
      subject: 'English',
      phone: '******-1002',
      qualification: 'M.A. English Literature',
      experience: '5'
    }
  ];

  return arrayToCSV(sampleData, TEACHER_CSV_HEADERS);
}

/**
 * Validate student data
 * @param {Object} student - Student data
 * @returns {Object} Validation result
 */
export function validateStudentData(student) {
  const errors = [];

  if (!student.name || student.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }

  if (!student.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(student.email)) {
    errors.push('Valid email is required');
  }

  if (!student.student_id || student.student_id.trim().length === 0) {
    errors.push('Student ID is required');
  }

  if (!student.grade || !['9', '10', '11', '12'].includes(student.grade)) {
    errors.push('Grade must be 9, 10, 11, or 12');
  }

  if (!student.section || !['A', 'B', 'C'].includes(student.section.toUpperCase())) {
    errors.push('Section must be A, B, or C');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate teacher data
 * @param {Object} teacher - Teacher data
 * @returns {Object} Validation result
 */
export function validateTeacherData(teacher) {
  const errors = [];

  if (!teacher.name || teacher.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }

  if (!teacher.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(teacher.email)) {
    errors.push('Valid email is required');
  }

  if (!teacher.teacher_id || teacher.teacher_id.trim().length === 0) {
    errors.push('Teacher ID is required');
  }

  if (!teacher.subject || teacher.subject.trim().length === 0) {
    errors.push('Subject is required');
  }

  if (teacher.experience && (isNaN(teacher.experience) || parseInt(teacher.experience) < 0)) {
    errors.push('Experience must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
